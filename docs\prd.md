### **<PERSON><PERSON><PERSON>-Lite: Proper Development Documentation for Agentic AI Coders**

**Project Goal:** Develop OAPIE-Lite, a desktop application (GUI) that acts as a Man-in-the-Middle (MITM) proxy to intercept, allow live manipulation of, and then relay LLM API messages to and from sources like Roo Code. It will also feature persistent logging to a PostgreSQL database and an integrated chatbox for direct LLM interaction, all controlled from the GUI.

**Key Features (<PERSON><PERSON><PERSON>-<PERSON><PERSON> Scope):**

1.  **MITM Proxy:** Intercepts LLM API calls (e.g., from Roo Code).
2.  **Live Message Manipulation:** Pauses intercepted requests, presents their content in the GUI for editing, and then forwards the modified message.
3.  **PostgreSQL Logging:** Stores "important parts" of all intercepted and relayed messages persistently.
4.  **Desktop GUI (CustomTkinter):** Provides a non-browser interface for proxy control, live manipulation, and an integrated LLM chatbox.
5.  **Integrated Chatbox:** Allows direct interaction with an LLM through the proxy, managed within the GUI.

---

**Documentation Structure Outline:**

Each section below represents a distinct module or set of instructions. Roo Code or a similar agent can tackle these step-by-step.

**Section 1: Project Setup & Initial Environment**
* **1.1. Project Directory Creation:** Command-line instruction.
* **1.2. Python Package Installation:** `pip` commands for all necessary libraries (`fastapi`, `uvicorn`, `httpx`, `customtkinter`, `psycopg2-binary`, `websockets`).
* **1.3. PostgreSQL Database & Table Initialization:** SQL commands for creating the `oapie_logs_db` database and the `messages_log` table with its defined schema (`id`, `timestamp`, `direction`, `etc.`).

**Section 2: Core Configuration Module (`config.py`)**
* **2.1. Purpose:** Centralize all configurable parameters for easy modification.
* **2.2. Contents:** Define specific Python variables for:
    * `TARGET_LLM_BASE_URL` (e.g., Gemini, OpenAI).
    * `LLM_API_KEY`.
    * `CHAT_LLM_MODEL`.
    * `PROXY_PORT`, `PROXY_HOST`.
    * `WEBSOCKET_URL`.
    * `GUI_PROXY_RELAY_ENDPOINT`.
    * `DB_NAME`, `DB_USER`, `DB_PASSWORD`, `DB_HOST`, `DB_PORT`.
* **2.3. Action:** Create `config.py` with these variables and placeholders for user-specific values.

**Section 3: Database Logging Module (`db_logger.py`)**
* **3.1. Purpose:** Provide an abstraction layer for all database interactions related to logging messages.
* **3.2. Responsibilities:**
    * Establish and manage PostgreSQL connections.
    * Insert structured log data into the `messages_log` table.
    * Extract relevant fields (model, prompt, response, tokens) from raw LLM request/response JSON bodies for storage.
* **3.3. Contents:** Define functions:
    * `get_db_connection()`: Connects to PostgreSQL.
    * `log_message_to_db(direction, message_type, data)`: Inserts log entry.
    * `extract_info_from_request(path, headers, body)`: Parses request for logging.
    * `extract_info_from_response(path, headers, body, status_code)`: Parses response for logging.
* **3.4. Action:** Create `db_logger.py` with these functions.

**Section 4: MITM Proxy Server (`proxy_server.py`)**
* **4.1. Purpose:** The core interception, manipulation, and relay component.
* **4.2. Responsibilities:**
    * Initialize FastAPI application.
    * Define a WebSocket endpoint (`/ws`) for GUI communication.
        * Handle incoming GUI messages (specifically `manipulated_message` with `request_id`).
        * Manage active WebSocket connections.
    * Define a POST endpoint (`/v1/{full_path:path}`) for LLM interception:
        * Generate unique `request_id`.
        * Store incoming request data, associate with `request_id`.
        * Send request data to GUI via WebSocket.
        * Pause execution, awaiting `manipulated_message` from GUI.
        * Upon receipt, update request body/headers.
        * Inject OAPIE-Lite's `LLM_API_KEY`.
        * Forward to `TARGET_LLM_BASE_URL`.
        * Log request and response using `db_logger`.
        * Send response info to GUI via WebSocket (for activity log).
    * Define a POST endpoint (`/v1/gui_relay`) for GUI's integrated chatbox messages:
        * Receive structured LLM request from GUI.
        * Inject OAPIE-Lite's `LLM_API_KEY`.
        * Forward to `TARGET_LLM_BASE_URL`.
        * Log request and response using `db_logger`.
        * Send response info to GUI via WebSocket.
    * Provide a `run_proxy_server()` function to start the Uvicorn server, suitable for execution in a separate thread/process.
* **4.3. Action:** Create `proxy_server.py` with this structure and logic.

**Section 5: Desktop GUI Application (`gui_app.py`)**
* **5.1. Purpose:** The user-facing interface for OAPIE-Lite.
* **5.2. Responsibilities:**
    * Initialize CustomTkinter application window.
    * Design GUI layout:
        * Proxy control buttons (`Start Proxy`, `Stop Proxy`).
        * Status labels.
        * Dedicated Textbox for "Live Interception & Manipulation" (for editing).
        * "Send Manipulated Message" button.
        * "Activity Log" Textbox for general status updates.
        * "OAPIE-Lite Chatbox" Textbox (display) and Entry (input) for integrated chat.
        * "Send Chat" button.
    * Implement proxy control logic:
        * `start_proxy()`: Launches `run_proxy_server` in a new thread.
        * `stop_proxy()`: Initiates WebSocket client closure and updates GUI status.
    * Implement WebSocket client connection and listener (`_websocket_connect_and_listen`):
        * Connects to `WEBSOCKET_URL`.
        * Receives messages from proxy (e.g., `intercepted_request`, `forwarded_response`, `gui_chat_request/response`).
        * Puts messages into a thread-safe queue for GUI processing.
    * Implement GUI update logic (`_process_gui_queue`):
        * Periodically checks the queue.
        * Updates status, populates "Live Interception & Manipulation" textbox (making it editable for incoming requests), updates "Activity Log", and updates "Chatbox" display.
    * Implement manipulation sending logic (`send_manipulated_message`):
        * Reads content from the "Live Interception & Manipulation" textbox.
        * Sends it back to the proxy via WebSocket with the associated `request_id`.
    * Implement chatbox sending logic (`Messages`):
        * Reads user input.
        * Constructs LLM request payload.
        * Sends to proxy's `GUI_PROXY_RELAY_ENDPOINT` (HTTP POST).
* **5.3. Action:** Create `gui_app.py` with this structure and functionality.

**Section 6: Running OAPIE-Lite**
* **6.1. Startup Sequence:** Step-by-step instructions for starting the GUI, then starting the proxy from the GUI.
* **6.2. Roo Code Configuration:** How to point Roo Code's API base URL to `http://localhost:8000/`.
* **6.3. Testing:** Steps to verify live interception, manipulation, logging, and chatbox functionality.
* **6.4. Shutdown:** Instructions for stopping the proxy from the GUI and resetting Roo Code's configuration.

---

I am ready to deliver this documentation, section by section, or as a complete document, depending on your preference. Please just tell me how you'd like to proceed from here.