# Task ID: 35
# Title: Implement GUI Logic to Send Manipulated Message
# Status: pending
# Dependencies: 32, 34
# Priority: high
# Description: Implement the logic for the 'Send Manipulated Message' button in `gui_app.py` to get the edited JSON from the manipulation textbox, construct a `manipulated_message` WebSocket message including the stored `request_id`, and send it back to the proxy.
# Details:
In `gui_app.py`, implement the function triggered by the 'Send Manipulated Message' button. Get the text content from the 'Live Interception & Manipulation' textbox. Retrieve the stored `request_id`. Construct a JSON message: `{'type': 'manipulated_message', 'request_id': stored_id, 'body': edited_json_string}`. Send this JSON message to the proxy via the WebSocket connection. Clear the manipulation textbox and update GUI status.

# Test Strategy:
Run the proxy and GUI. Send an LLM request. When the JSON appears in the manipulation textbox, edit it slightly. Click 'Send Manipulated Message'. Verify a message with `type: 'manipulated_message'`, the correct `request_id`, and the edited body is sent from the GUI to the proxy via WebSocket (check logs).
