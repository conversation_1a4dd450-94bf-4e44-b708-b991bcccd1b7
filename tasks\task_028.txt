# Task ID: 28
# Title: Implement Basic GUI Structure and Proxy Control
# Status: pending
# Dependencies: 21, 22
# Priority: high
# Description: Implement the basic CustomTkinter GUI structure in `gui_app.py`, including the main window, 'Start Proxy' and 'Stop Proxy' buttons, and an 'Activity Log' textbox.
# Details:
Create `gui_app.py`. Initialize `customtkinter.CTk()` window. Create `CTkButton` for 'Start Proxy' and 'Stop Proxy'. Implement `start_proxy()` and `stop_proxy()` functions. `start_proxy()` should run `proxy_server.run_proxy_server()` in a separate thread (to keep the GUI responsive). `stop_proxy()` should signal the proxy thread to stop (initial implementation can be basic). Create a `CTkTextbox` for the 'Activity Log' and a helper function `append_to_activity_log(message)` to add text to it.

# Test Strategy:
Run `gui_app.py`. Verify the window appears with the correct buttons and textbox. Click 'Start Proxy' and verify the proxy server starts (check console output or a health check). Click 'Stop Proxy' and verify the proxy server attempts to shut down.
