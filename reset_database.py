#!/usr/bin/env python3
"""
Database Reset Script for OAPIE-Lite.

This script helps reset a corrupted SQLite database by backing up the old one
and creating a fresh database with the correct schema.
"""

import os
import sys
import sqlite3
import datetime
import logging

# Add the current directory to the path so we can import from src
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config import USE_SQLITE, SQLITE_DB_PATH

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# SQL for creating the messages_log table
SQLITE_CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS messages_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    direction TEXT NOT NULL,
    message_type TEXT NOT NULL,
    path TEXT,
    model TEXT,
    prompt TEXT,
    response TEXT,
    tokens INTEGER,
    status_code INTEGER,
    raw_data TEXT
);
"""

def backup_existing_database():
    """Backup the existing database file if it exists."""
    if os.path.exists(SQLITE_DB_PATH):
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{SQLITE_DB_PATH}.backup.{timestamp}"
        
        try:
            os.rename(SQLITE_DB_PATH, backup_path)
            logger.info(f"Existing database backed up to: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"Failed to backup existing database: {e}")
            return None
    else:
        logger.info("No existing database file found.")
        return None

def create_fresh_database():
    """Create a fresh SQLite database with the correct schema."""
    try:
        # Create new connection with proper encoding
        conn = sqlite3.connect(SQLITE_DB_PATH, check_same_thread=False)
        
        # Set text encoding to UTF-8
        conn.execute("PRAGMA encoding = 'UTF-8';")
        
        # Create the table
        conn.execute(SQLITE_CREATE_TABLE_SQL)
        conn.commit()
        
        # Test the connection
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        logger.info(f"Created fresh database with tables: {[table[0] for table in tables]}")
        
        conn.close()
        logger.info(f"Fresh database created successfully at: {SQLITE_DB_PATH}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create fresh database: {e}")
        return False

def test_database_connection():
    """Test the database connection to ensure it's working."""
    try:
        conn = sqlite3.connect(SQLITE_DB_PATH)
        
        # Test basic operations
        conn.execute("SELECT COUNT(*) FROM messages_log;")
        
        # Test inserting a sample record
        conn.execute("""
            INSERT INTO messages_log 
            (direction, message_type, path, model, prompt, response, tokens, status_code, raw_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, ("test", "test", "/test", "test-model", "test prompt", "test response", 10, 200, "{}"))
        
        conn.commit()
        
        # Test reading the record
        cursor = conn.execute("SELECT * FROM messages_log WHERE direction = 'test';")
        result = cursor.fetchone()
        
        if result:
            logger.info("Database test successful - can read and write data")
            
            # Clean up test record
            conn.execute("DELETE FROM messages_log WHERE direction = 'test';")
            conn.commit()
        else:
            logger.error("Database test failed - could not read inserted data")
            return False
            
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"Database test failed: {e}")
        return False

def main():
    """Main function to reset the database."""
    logger.info("Starting database reset process...")
    
    if not USE_SQLITE:
        logger.error("This script is only for SQLite databases. Current configuration uses PostgreSQL.")
        return False
    
    # Step 1: Backup existing database
    backup_path = backup_existing_database()
    
    # Step 2: Create fresh database
    if not create_fresh_database():
        logger.error("Failed to create fresh database")
        return False
    
    # Step 3: Test the new database
    if not test_database_connection():
        logger.error("Database test failed after creation")
        return False
    
    logger.info("Database reset completed successfully!")
    
    if backup_path:
        logger.info(f"Your old database was backed up to: {backup_path}")
        logger.info("You can delete this backup file if you don't need the old data.")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
    else:
        print("\n✅ Database reset completed successfully!")
        print("You can now run your application again.")
