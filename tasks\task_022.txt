# Task ID: 22
# Title: Define Configuration File (config.py)
# Status: pending
# Dependencies: None
# Priority: high
# Description: Define and populate the central configuration file (config.py) with settings for LLM API key, proxy port, WebSocket endpoint, and PostgreSQL database credentials.
# Details:
Create a config.py file. Define variables for:
LLM_API_KEY = 'your_llm_api_key'
PROXY_HOST = '127.0.0.1'
PROXY_PORT = 8000
WS_HOST = '127.0.0.1'
WS_PORT = 8000
DB_HOST = 'localhost'
DB_PORT = 5432
DB_NAME = 'oapie_logs_db'
DB_USER = 'your_db_user'
DB_PASSWORD = 'your_db_password'
LLM_API_BASE_URL = 'https://api.openai.com/v1' # Example, adjust based on LLM provider

# Test Strategy:
Ensure config.py is created with all required variables defined and accessible.
