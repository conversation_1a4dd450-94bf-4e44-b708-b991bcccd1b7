# Task ID: 34
# Title: Implement GUI WebSocket Handling for Intercepted Requests
# Status: pending
# Dependencies: 31, 32
# Priority: high
# Description: Implement WebSocket message handling in `gui_app.py` to receive `intercepted_request` messages from the proxy, display the request <PERSON><PERSON><PERSON> in the manipulation textbox, enable editing, and store the associated `request_id`.
# Details:
In `gui_app.py`, modify the `_process_gui_queue()` function to handle messages with `type: 'intercepted_request'`. When such a message is received, extract the `request_id`, `headers`, and `body`. Store the `request_id` in a class variable (`self.current_request_id`). Display the `body` (formatted JSON, e.g., using `json.dumps(..., indent=2)`) in the 'Live Interception & Manipulation' textbox. Configure this textbox to `state="normal"` (editable). Enable the 'Send Manipulated Message' button. Update the GUI status or activity log to indicate a request is pending manipulation.

# Test Strategy:
Run the proxy and G<PERSON>. Send an LLM request from a client. Verify the request <PERSON><PERSON><PERSON> appears in the 'Live Interception & Manipulation' textbox, which becomes editable, and the 'Send Manipulated Message' button becomes enabled. The GUI's activity log should indicate a pending manipulation.
