# Task ID: 25
# Title: Implement Initial Data Extraction Functions
# Status: pending
# Dependencies: 24
# Priority: medium
# Description: Implement initial placeholder functions (`extract_info_from_request`, `extract_info_from_response`) in `db_logger.py` to extract key details from LLM API request and response bodies.
# Details:
In `db_logger.py`, add `extract_info_from_request(path, headers, body)` and `extract_info_from_response(path, headers, body, status_code)` functions. These should parse common LLM API JSON structures (like OpenAI's chat completions or Gemini's generateContent) to find the main prompt/response text, model name, and potentially estimate tokens. Ensure they handle cases where fields might be missing gracefully.

# Test Strategy:
Write unit tests for the extraction functions using sample LLM request and response JSON payloads to ensure they correctly identify and return the required fields (model, prompt, response, etc.).
