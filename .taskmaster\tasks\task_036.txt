# Task ID: 36
# Title: Implement Proxy WebSocket Handling for Manipulated Messages
# Status: pending
# Dependencies: 33, 35
# Priority: high
# Description: Implement WebSocket message handling in `proxy_server.py` to receive `manipulated_message` messages from the GUI, retrieve the corresponding paused request using the `request_id`, update its body with the manipulated content, and resume the request by setting the result of the associated `asyncio.Future`.
# Details:
In the `websocket_endpoint` handler in `proxy_server.py`, add logic to process messages with `type: 'manipulated_message'`. Extract the `request_id` and `manipulated_body`. Look up the stored `asyncio.Future` in `pending_requests` using the `request_id`. Set the result of the `asyncio.Future` to `manipulated_body`, which will unblock the waiting `proxy_llm_request` endpoint. Handle cases where the `request_id` is not found (e.g., timeout occurred or invalid ID) by printing a warning. Remove the request from `pending_requests` after processing.

# Test Strategy:
Run the proxy and GUI. Send an LLM request. When the JSON appears in the GUI, edit it and click 'Send Manipulated Message'. Verify the proxy receives the manipulated message, updates the request body internally, and the request successfully proceeds to the LLM provider with the *modified* body. Verify the LLM response is received by the original client and logged correctly in the DB.
