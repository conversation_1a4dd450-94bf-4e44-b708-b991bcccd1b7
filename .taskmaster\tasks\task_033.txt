# Task ID: 33
# Title: Enhance Proxy Interceptor for Pausing and Sending to GUI
# Status: pending
# Dependencies: 27, 31
# Priority: high
# Description: Enhance the core HTTP proxy interceptor (`/v1/{full_path:path}`) in `proxy_server.py` to pause the request processing, assign a unique request ID, store the request context, and send the request body and ID to the GUI via WebSocket.
# Details:
In the `proxy_llm_request` endpoint in `proxy_server.py`, before forwarding the request: Generate a unique `request_id` (e.g., using `uuid.uuid4()`). Store the request object or necessary context (headers, original URL, etc.) and an `asyncio.Future` in a dictionary keyed by `request_id` (e.g., `pending_requests`). Send a JSON message via WebSocket to all connected GUIs containing `type: 'intercepted_request'`, `request_id`, the request `headers`, and the request `body`. Await the result of the `asyncio.Future` with a timeout (e.g., 60 seconds). The request processing will pause here until the Future is set by the WebSocket handler receiving the manipulated message from the GUI. Handle `asyncio.TimeoutError` by returning an appropriate HTTP error.

# Test Strategy:
Run the proxy and GUI. Send an LLM request from a client. Verify the request is paused (it should not immediately reach the LLM or return a response). Verify a message with `type: 'intercepted_request'`, `request_id`, and the request body/headers is sent from the proxy to the GUI via WebSocket (check GUI's activity log/interception display).
