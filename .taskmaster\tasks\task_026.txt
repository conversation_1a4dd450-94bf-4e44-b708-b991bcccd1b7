# Task ID: 26
# Title: Setup Proxy Server Basic Structure and GUI Relay Endpoint
# Status: pending
# Dependencies: 22
# Priority: high
# Description: Set up the basic FastAPI application structure in `proxy_server.py`, configure Uvicorn, add a health check endpoint (`/health`), and implement the `/v1/gui_relay` endpoint for the GUI chatbox.
# Details:
Create `proxy_server.py`. Initialize `FastAPI()` app. Define an async function `run_proxy_server()` that uses `uvicorn.run()` to start the app on `config.PROXY_HOST` and `config.PROXY_PORT`. Add `log_level="warning"` to `uvicorn.run()` to suppress verbose logs. Add a simple GET endpoint `/health` that returns `{'status': 'ok'}`. Implement an async POST endpoint `/v1/gui_relay` that accepts a JSON body (representing a chat request from the GUI). This endpoint will initially just log the received body or return a placeholder response.

# Test Strategy:
Run `proxy_server.py`. Use `curl` or a browser to access `/health` and verify the response. Use `curl` or a tool like <PERSON><PERSON> to send a POST request to `/v1/gui_relay` with a JSON body and verify the server receives it (e.g., via console print).

# Subtasks:
## 1. Initialize FastAPI Application [pending]
### Dependencies: None
### Description: Create the main FastAPI application instance and set up the basic project structure.
### Details:
Set up the project directory, create the main application file (e.g., main.py), and instantiate the FastAPI app object.

## 2. Configure Uvicorn Server [pending]
### Dependencies: 26.1
### Description: Set up Uvicorn as the ASGI server to run the FastAPI application.
### Details:
Add Uvicorn to project dependencies and configure how to run the application using Uvicorn (e.g., via a command or script). Ensure `log_level="warning"` is set for cleaner output.

## 3. Implement Health Check Endpoint [pending]
### Dependencies: 26.1
### Description: Add a simple endpoint to verify the server is running.
### Details:
Create a GET endpoint (e.g., `/health`) that returns a basic success response, like a JSON object `{"status": "ok"}`.

## 4. Implement Basic GUI Relay Endpoint [pending]
### Dependencies: 26.1
### Description: Create the initial endpoint structure for relaying data from the GUI.
### Details:
Define the endpoint path and method (e.g., POST `/gui/relay`) that will receive data from the GUI. Implement basic request handling, potentially just logging the received data or returning a placeholder response.

