"""
Database Logger Module for OAPIE-Lite.

This module provides an abstraction layer for all database interactions related to logging messages.
It supports both PostgreSQL and SQLite databases for flexibility, with automatic fallback and
retry mechanisms for handling connection issues.

Key Components:
1. Database Connection Management:
   - Connection establishment with retry logic
   - Support for both PostgreSQL and SQLite
   - Automatic table creation

2. Message Logging:
   - Log requests and responses
   - Extract relevant information from different LLM API formats
   - Store raw data and structured fields

3. Query Interface:
   - Retrieve logged messages with filtering options
   - Parse and format results for display

Usage:
    Import and use the functions directly:
    ```
    from src.db_logger import log_message_to_db, query_logs
    
    # Log a message
    log_message_to_db(
        direction="request",
        message_type="intercepted",
        data=request_body,
        path="/v1/chat/completions",
        model="gpt-3.5-turbo"
    )
    
    # Query logs
    logs = query_logs(limit=10, direction="request")
    ```
"""

import json
import sqlite3
import datetime
import logging
from typing import Dict, Any, Optional, Union, Tuple

import psycopg2
from psycopg2.extras import RealDictCursor

from src.config import (
    DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD,
    USE_SQLITE, SQLITE_DB_PATH
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# SQL statements for table creation
POSTGRES_CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS messages_log (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    direction TEXT NOT NULL,
    message_type TEXT NOT NULL,
    path TEXT,
    model TEXT,
    prompt TEXT,
    response TEXT,
    tokens INTEGER,
    status_code INTEGER,
    raw_data JSONB
);
"""

SQLITE_CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS messages_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    direction TEXT NOT NULL,
    message_type TEXT NOT NULL,
    path TEXT,
    model TEXT,
    prompt TEXT,
    response TEXT,
    tokens INTEGER,
    status_code INTEGER,
    raw_data TEXT
);
"""

def get_db_connection(retry_count=3, retry_delay=1.0):
    """
    Establishes and returns a database connection based on configuration.

    Includes retry logic for handling transient connection issues.

    Args:
        retry_count: Number of connection attempts before giving up
        retry_delay: Delay between retries in seconds (will increase with each retry)

    Returns:
        Connection object (either psycopg2 or sqlite3) or None if all retries fail
    """
    last_error = None
    current_retry = 0
    current_delay = retry_delay

    while current_retry <= retry_count:
        try:
            if USE_SQLITE:
                # Check if database file exists and is corrupted
                import os
                if os.path.exists(SQLITE_DB_PATH):
                    try:
                        # Try to open and test the database
                        test_conn = sqlite3.connect(SQLITE_DB_PATH)
                        test_conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
                        test_conn.close()
                    except (sqlite3.DatabaseError, UnicodeDecodeError) as db_error:
                        logger.warning(f"Database file appears corrupted: {db_error}. Creating new database.")
                        # Backup the corrupted file
                        backup_path = f"{SQLITE_DB_PATH}.corrupted.{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        os.rename(SQLITE_DB_PATH, backup_path)
                        logger.info(f"Corrupted database backed up to: {backup_path}")

                # Create new connection with proper encoding
                conn = sqlite3.connect(SQLITE_DB_PATH, check_same_thread=False)
                # Set text encoding to UTF-8
                conn.execute("PRAGMA encoding = 'UTF-8';")
                conn.execute(SQLITE_CREATE_TABLE_SQL)
                conn.commit()
                logger.info(f"Connected to SQLite database: {SQLITE_DB_PATH}")
                return conn
            else:
                conn = psycopg2.connect(
                    host=DB_HOST,
                    port=DB_PORT,
                    dbname=DB_NAME,
                    user=DB_USER,
                    password=DB_PASSWORD,
                    cursor_factory=RealDictCursor,
                    connect_timeout=5  # Add timeout to avoid hanging
                )
                with conn.cursor() as cursor:
                    cursor.execute(POSTGRES_CREATE_TABLE_SQL)
                    conn.commit()
                logger.info(f"Connected to PostgreSQL database: {DB_NAME} at {DB_HOST}:{DB_PORT}")
                return conn
        except Exception as e:
            last_error = e
            current_retry += 1

            if current_retry <= retry_count:
                logger.warning(f"Database connection attempt {current_retry} failed: {e}. Retrying in {current_delay:.1f}s...")
                import time
                time.sleep(current_delay)
                # Exponential backoff
                current_delay *= 1.5
            else:
                logger.error(f"All database connection attempts failed. Last error: {e}")
                return None  # Return None instead of raising to allow graceful degradation

    # This should not be reached, but just in case
    logger.error(f"Database connection failed after {retry_count} attempts. Last error: {last_error}")
    return None

def log_message_to_db(
    direction: str,
    message_type: str,
    data: Dict[str, Any],
    path: Optional[str] = None,
    model: Optional[str] = None,
    prompt: Optional[str] = None,
    response: Optional[str] = None,
    tokens: Optional[int] = None,
    status_code: Optional[int] = None
) -> bool:
    """
    Inserts a log entry into the database.
    
    Args:
        direction: 'request' or 'response'
        message_type: Type of message (e.g., 'intercepted', 'forwarded', 'chat')
        data: Raw message data
        path: API endpoint path
        model: LLM model name
        prompt: User prompt text
        response: LLM response text
        tokens: Token count
        status_code: HTTP status code (for responses)
        
    Returns:
        bool: Success status
    """
    try:
        conn = get_db_connection()
        
        # Handle case where connection failed
        if conn is None:
            logger.error("Failed to log message: database connection unavailable")
            return False
            
        if USE_SQLITE:
            # SQLite needs JSON as string
            raw_data_str = json.dumps(data)
            
            with conn:
                conn.execute(
                    """
                    INSERT INTO messages_log 
                    (direction, message_type, path, model, prompt, response, tokens, status_code, raw_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (direction, message_type, path, model, prompt, response, tokens, status_code, raw_data_str)
                )
        else:
            # PostgreSQL can store JSON natively
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO messages_log 
                    (direction, message_type, path, model, prompt, response, tokens, status_code, raw_data)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    (direction, message_type, path, model, prompt, response, tokens, status_code, json.dumps(data))
                )
                conn.commit()
        
        logger.debug(f"Logged {direction} {message_type} message to database")
        return True
    except Exception as e:
        logger.error(f"Error logging message to database: {e}")
        return False
    finally:
        # Only close if connection was successful and it's PostgreSQL
        if conn is not None and not USE_SQLITE:
            conn.close()

def extract_info_from_request(path: str, headers: Dict[str, str], body: Dict[str, Any]) -> Tuple[str, str, int]:
    """
    Parses a request to extract relevant information for logging.
    
    Args:
        path: API endpoint path
        headers: Request headers
        body: Request body
        
    Returns:
        Tuple of (model, prompt, estimated_tokens)
    """
    model = None
    prompt = None
    estimated_tokens = 0
    
    try:
        # Extract model information
        if "model" in body:
            model = body["model"]
        
        # Extract prompt based on API structure
        if "messages" in body:
            # OpenAI-style chat completion
            messages = body.get("messages", [])
            prompt = "\n".join([f"{m.get('role', 'unknown')}: {m.get('content', '')}" for m in messages])
            # Rough token estimation (4 chars ≈ 1 token)
            estimated_tokens = len(prompt) // 4
        elif "prompt" in body:
            # Simple prompt structure
            prompt = body["prompt"]
            estimated_tokens = len(prompt) // 4
        elif "contents" in body:
            # Gemini-style API
            contents = body.get("contents", [])
            prompt = "\n".join([part.get("text", "") for part in contents if "text" in part])
            estimated_tokens = len(prompt) // 4
            
    except Exception as e:
        logger.error(f"Error extracting info from request: {e}")
    
    return model, prompt, estimated_tokens

def extract_info_from_response(
    path: str, 
    headers: Dict[str, str], 
    body: Dict[str, Any], 
    status_code: int
) -> Tuple[str, int]:
    """
    Parses a response to extract relevant information for logging.
    
    Args:
        path: API endpoint path
        headers: Response headers
        body: Response body
        status_code: HTTP status code
        
    Returns:
        Tuple of (response_text, tokens)
    """
    response_text = None
    tokens = 0
    
    try:
        # Extract response based on API structure
        if "choices" in body and len(body["choices"]) > 0:
            # OpenAI-style response
            choice = body["choices"][0]
            if "message" in choice and "content" in choice["message"]:
                response_text = choice["message"]["content"]
            elif "text" in choice:
                response_text = choice["text"]
                
            # Get token usage if available
            if "usage" in body:
                tokens = body["usage"].get("total_tokens", 0)
                
        elif "candidates" in body and len(body["candidates"]) > 0:
            # Gemini-style response
            candidate = body["candidates"][0]
            if "content" in candidate and "parts" in candidate["content"]:
                parts = candidate["content"]["parts"]
                response_text = "\n".join([part.get("text", "") for part in parts if "text" in part])
                
            # Rough token estimation for Gemini (4 chars ≈ 1 token)
            if response_text:
                tokens = len(response_text) // 4
                
    except Exception as e:
        logger.error(f"Error extracting info from response: {e}")
    
    return response_text, tokens

def query_logs(
    limit: int = 100, 
    direction: Optional[str] = None,
    message_type: Optional[str] = None
) -> list:
    """
    Query the database for log entries.
    
    Args:
        limit: Maximum number of records to return
        direction: Filter by direction ('request' or 'response')
        message_type: Filter by message type
        
    Returns:
        List of log entries
    """
    try:
        conn = get_db_connection()
        
        # Handle case where connection failed
        if conn is None:
            logger.error("Failed to query logs: database connection unavailable")
            return []
            
        query = "SELECT * FROM messages_log"
        params = []
        
        # Build WHERE clause if filters are provided
        where_clauses = []
        if direction:
            where_clauses.append("direction = ?")
            params.append(direction)
        if message_type:
            where_clauses.append("message_type = ?")
            params.append(message_type)
            
        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)
            
        # Add order and limit
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        if USE_SQLITE:
            cursor = conn.execute(query, params)
            # Convert SQLite rows to dictionaries
            columns = [column[0] for column in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            # Parse JSON strings in SQLite
            for row in results:
                if 'raw_data' in row and row['raw_data']:
                    try:
                        row['raw_data'] = json.loads(row['raw_data'])
                    except json.JSONDecodeError:
                        # Handle invalid JSON
                        row['raw_data'] = {"error": "Invalid JSON data"}
                    
            return results
        else:
            # Convert ? placeholders to %s for PostgreSQL
            query = query.replace("?", "%s")
            
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
                
    except Exception as e:
        logger.error(f"Error querying logs: {e}")
        return []
    finally:
        # Only close if connection was successful and it's PostgreSQL
        if conn is not None and not USE_SQLITE:
            conn.close()