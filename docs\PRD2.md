Product Requirement Document (PRD) - OAPIE-Lite

# Overview
OAPIE-Lite is a desktop application designed to provide developers and AI researchers with transparent control and deep insight into their Large Language Model (LLM) API interactions. It acts as a local Man-in-the-Middle (MITM) proxy, allowing users to intercept, inspect, and actively manipulate LLM API request messages originating from applications like Roo Code before they are sent to the LLM provider. OAPIE-Lite records all significant LLM traffic to a local PostgreSQL database for audit and analysis, and includes an integrated chatbox for direct, proxied LLM interaction.

Problem Solved:

Lack of Visibility: Developers often lack real-time insight into the exact API requests being sent by LLM-powered tools or their own applications, making debugging and understanding LLM behavior challenging.
Limited Control: Existing tools rarely allow for on-the-fly modification of LLM prompts or parameters before a request reaches the LLM provider.
API Key Management (Proxy-side): Provides a centralized point where the proxy injects its own configured API key, abstracting key management from client applications.
Auditing & Analysis: No easy way to log and review LLM interactions persistently and effectively.
Who it's for:

Developers and AI Engineers: Working with LLMs, especially those integrating LLMs into IDEs (like Roo Code) or custom applications.
Teams: Needing to monitor, debug, or enforce policies on LLM usage.
Individuals: Seeking granular control over their LLM interactions for experimentation or learning.
Why it's valuable:

Enhanced Debugging: See exactly what prompts are being sent and what responses are received.
Experimentation: Easily modify prompts, parameters, or even model choices on the fly without changing source code.
Security & Control: Centralize API key usage and gain a clear audit trail of all LLM interactions.
Accelerated Development: Rapidly iterate on LLM prompts and configurations.
# Core Features

1. MITM Proxy for LLM API Calls

What it does: Intercepts HTTP requests (specifically POST requests to common LLM API paths like /v1/chat/completions or /v1/models/{model}:generateContent) from a configured client application (e.g., Roo Code) to an LLM provider.
Why it's important: Forms the foundation of OAPIE-Lite, enabling all other features by sitting directly in the communication path. It makes message inspection and manipulation possible.
How it works at a high level:
Listens on a configurable local port (e.g., http://localhost:8000).
Receives requests from the client (e.g., Roo Code).
Processes the request according to OAPIE-Lite's logic (pause, manipulate, log).
Forwards the request to the actual LLM provider's API.
Receives the LLM's response and forwards it back to the client.
2. Live Message Manipulation (Pause-Edit-Resume)

What it does: Upon intercepting an incoming LLM request, OAPIE-Lite pauses its forwarding, sends the request's content to the desktop GUI for display and editing, waits for user manipulation, and then relays the modified message to the LLM.
Why it's important: This is the core interactive control feature, allowing real-time, granular adjustments to prompts, parameters, or even LLM models without altering the client application's code.
How it works at a high level:
Proxy receives an LLM request, assigns it a unique ID, and temporarily holds it.
Proxy sends the request's JSON body and its ID to the GUI via a WebSocket connection.
GUI displays the request in an editable text-box.
User modifies the JSON in the text-box.
User clicks "Send Manipulated Message" button.
GUI sends the modified JSON and request ID back to the proxy via the WebSocket.
Proxy retrieves the held request using the ID, updates its body with the manipulated content, and resumes forwarding to the LLM.
3. PostgreSQL Logging

What it does: Stores key details of all intercepted requests and their corresponding LLM responses into a local PostgreSQL database.
Why it's important: Provides a persistent, structured, and searchable record of all LLM interactions, essential for debugging, auditing, cost analysis, and understanding usage patterns.
How it works at a high level:
A dedicated Python module (db_logger.py) handles all database connections and SQL operations.
Before forwarding an intercepted request, the proxy extracts "important parts" (e.g., model, prompt, headers, full body) and logs them.
Upon receiving an LLM response, the proxy extracts "important parts" (e.g., status code, response text, headers, full body) and logs them, often linking to the original request.
4. Desktop GUI (CustomTkinter)

What it does: Provides the primary user interface for OAPIE-Lite, running as a standalone application on the user's desktop, avoiding browser dependencies.
Why it's important: Delivers the interactive experience the user needs, combining proxy control, live manipulation, and chat functionalities in a single, dedicated window.
How it works at a high level:
Built using CustomTkinter for a modern, native look.
Features buttons to start/stop the local proxy server.
Contains dedicated text areas for live message manipulation and a general activity log.
Houses the integrated LLM chatbox.
Manages WebSocket client connection to the proxy for real-time updates and command-and-control.
5. Integrated Chatbox

What it does: A text-based chat interface within the GUI that allows users to directly interact with an LLM. All chat messages sent via this feature are routed through the OAPIE-Lite proxy.
Why it's important: Enables quick testing of LLM responses and demonstrates the proxy's functionality without needing to configure an external application like Roo Code. Ensures all manual LLM interactions are also logged and pass through OAPIE-Lite's logic.
How it works at a high level:
User types a message into an input field in the GUI.
Upon sending, the GUI constructs an LLM API request payload.
This payload is sent via a dedicated HTTP POST endpoint on the OAPIE-Lite proxy (/v1/gui_relay).
The proxy processes and forwards this request to the LLM, logs it, and sends the LLM's response back to the GUI for display.
# User Experience

User Personas:

"The Tinkerer" (Individual Developer/AI Enthusiast): Uses OAPIE-Lite to understand how LLMs respond to subtle prompt changes, debug complex AI chain interactions, and experiment with different LLM models and parameters without code changes. Values immediate feedback and transparent logging.
"The Team Lead" (Project Manager/Senior Developer): Needs to monitor LLM usage within a development team, enforce API key best practices, and audit AI-driven features for compliance or cost management. Values reliable logging and a clear overview of traffic.
Key User Flows:

Initial Setup:
User installs Python packages.
User ensures PostgreSQL is running and creates oapie_logs_db and messages_log table.
User fills in config.py with LLM API key and DB credentials.
User starts gui_app.py.
Starting Interception:
User clicks "Start Proxy" button in the OAPIE-Lite GUI.
GUI updates status to "Proxy Running" and connects WebSocket.
User configures Roo Code (or other client) to use http://localhost:8000/.
Live Message Manipulation:
Roo Code sends an LLM request.
OAPIE-Lite intercepts, the GUI displays the request JSON in the "Live Interception & Manipulation" textbox (now editable).
User modifies the JSON (e.g., changes a word in the prompt, adjusts a temperature parameter).
User clicks "Send Manipulated Message."
The modified message is sent to the LLM, and the response is relayed back to Roo Code.
Activity log updates with interception and forwarding events.
Direct Chat Interaction:
User types a message into the "OAPIE-Lite Chatbox" input field.
User presses Enter or clicks "Send Chat."
The chat exchange is displayed in the chatbox, and events are logged in the activity log.
Stopping Interception:
User clicks "Stop Proxy" button in the OAPIE-Lite GUI.
GUI updates status to "Proxy Stopped."
User reconfigures Roo Code to point directly to the LLM API.
Reviewing Logs: (MVP via direct DB query for now)
User accesses their PostgreSQL client to view messages_log table content.
UI/UX Considerations:

Clarity: Clear labels for sections (Proxy Control, Live Interception, Activity Log, Chatbox).
Responsiveness: GUI remains interactive while the proxy is running and waiting for manipulation.
Feedback: Status messages in the GUI (e.g., "Proxy Running", "Waiting for manipulation", "Error").
JSON Display: Use syntax highlighting (if CustomTkinter allows easily, otherwise plain text with indentation) for intercepted JSON for readability.
Intuitive Flow: The visual layout should guide the user through the interception, manipulation, and sending process.
# Technical Architecture

System Components:

1. OAPIE-Lite Proxy Server (proxy_server.py):
Technology: Python, FastAPI, Uvicorn (ASGI server), httpx (async HTTP client), websockets (WebSocket server).
Role: Handles HTTP interception, WebSocket communication with GUI, LLM API calls, and logging coordination.
2. OAPIE-Lite Desktop GUI (gui_app.py):
Technology: Python, customtkinter (GUI framework), websockets (WebSocket client), httpx (for GUI chat relay).
Role: User interface, proxy control, message manipulation display, chat interface.
3. Database Logging Module (db_logger.py):
Technology: Python, psycopg2-binary (PostgreSQL adapter).
Role: Encapsulates all PostgreSQL connection and data insertion logic.
4. Central Configuration (config.py):
Technology: Python module.
Role: Stores all environment-specific settings (API keys, ports, DB credentials, URLs).
5. PostgreSQL Database:
Technology: PostgreSQL RDBMS.
Role: Persistent storage for all message logs.
Data Models:

messages_log Table (PostgreSQL):
id (SERIAL PRIMARY KEY)
timestamp (TIMESTAMP WITH TIME ZONE, DEFAULT CURRENT_TIMESTAMP)
direction (VARCHAR(10), e.g., 'INCOMING', 'OUTGOING')
message_type (VARCHAR(10), e.g., 'REQUEST', 'RESPONSE')
llm_model (VARCHAR(50), e.g., 'gemini-pro', 'gpt-4')
request_path (TEXT, the API endpoint path, e.g., '/v1/chat/completions')
status_code (INTEGER, HTTP status code for responses)
prompt_text (TEXT, extracted main prompt)
response_text (TEXT, extracted main response)
token_count_in (INTEGER, estimated input token count)
token_count_out (INTEGER, estimated output token count)
full_headers (JSONB, full HTTP headers for debugging)
full_body (JSONB, full JSON request/response body for debugging)
APIs and Integrations:

HTTP Proxying:
Client (e.g., Roo Code) -> Proxy: Standard HTTP/S (configurable to http://localhost:8000/v1/).
Proxy -> LLM Provider: Standard HTTP/S POST requests (e.g., to https://generativelanguage.googleapis.com/v1/models/gemini-pro:generateContent).
WebSocket Communication (Proxy <-> GUI):
Purpose: Real-time, asynchronous two-way communication for live message interception data transfer and manipulated message return.
Endpoint: ws://localhost:8000/ws.
Messages: JSON objects containing type (e.g., intercepted_request, manipulated_message, forwarded_response, gui_chat_request, gui_chat_response), request_id, and relevant data (body, headers, status_code).
REST API (GUI -> Proxy):
Endpoint: http://localhost:8000/v1/gui_relay (HTTP POST).
Purpose: For the GUI's integrated chatbox to send new LLM requests through the proxy.
Database Integration:
Proxy/Logger -> PostgreSQL: psycopg2-binary for Python-to-PostgreSQL connection and SQL execution.
Infrastructure Requirements:

Operating System: Cross-platform (Windows, macOS, Linux) due to Python and CustomTkinter.
Runtime: Python 3.8+ environment.
Database: Locally running PostgreSQL database instance (version 10 or higher recommended for JSONB).
Networking: Loopback interface (127.0.0.1 or localhost) available for proxy and WebSocket communication.
# Development Roadmap

This roadmap prioritizes establishing core functionality quickly, then layering on the interactive features. It is designed to be atomic and buildable for an agentic AI coder.

Phase 1: Foundation & Basic Logging

Objective: Set up the project structure, basic configuration, and functional PostgreSQL logging.
Tasks:
1.1. Create project directory (oapie_lite_gui).
1.2. Install all required Python packages (fastapi, uvicorn, httpx, customtkinter, psycopg2-binary, websockets).
1.3. Define and populate config.py with LLM, Proxy, WebSocket, and PostgreSQL settings.
1.4. Implement db_logger.py:
get_db_connection() function.
log_message_to_db() function.
Initial extract_info_from_request() and extract_info_from_response() functions (can be basic placeholders initially).
Phase 2: Core Proxy & Basic GUI (Visible Front-End)

Objective: Get a functional proxy that logs traffic to the DB, and a minimal GUI that can start/stop the proxy and show general activity.
Tasks:
2.1. Implement proxy_server.py (initial version):
Initialize FastAPI app.
Implement basic HTTP POST proxy endpoint (/v1/{full_path:path}) to intercept, inject API key, forward to LLM, and log responses via db_logger.py.
Add a simple / health check endpoint.
Define run_proxy_server() function.
2.2. Implement gui_app.py (initial version):
Initialize CustomTkinter application window.
Create "Start Proxy" and "Stop Proxy" buttons.
Implement logic for start_proxy() to launch run_proxy_server in a separate thread.
Implement stop_proxy() (initial simple shutdown).
Create an "Activity Log" textbox.
Implement append_to_activity_log() to display simple status messages and basic proxy events (e.g., "Request received", "Response forwarded").
Create a simple integrated chatbox (input field, display textbox, send button).
Implement Messages() to send chat messages via HTTP POST to a new proxy endpoint (/v1/gui_relay).
2.3. Add /v1/gui_relay endpoint to proxy_server.py to receive messages from the GUI chatbox and relay them to the LLM, logging them.
Phase 3: Live Interception & Manipulation (MVP Completion)

Objective: Implement the core "pause-edit-resume" functionality for intercepted messages using WebSockets.
Tasks:
3.1. Enhance proxy_server.py:
Implement WebSocket endpoint (/ws).
Introduce pending_requests dictionary and asyncio.Future for pausing/resuming requests.
Modify /v1/{full_path:path} endpoint to: assign request_id, send request data to GUI via WS, and await Future completion.
Implement WebSocket message handling for manipulated_message (from GUI), setting the result of the corresponding Future.
Add send_to_gui() helper function for proxy-to-GUI WS messages.
3.2. Enhance gui_app.py:
Implement WebSocket client connection (_websocket_connect_and_listen).
Modify _process_gui_queue() to handle new WebSocket message types (e.g., intercepted_request, forwarded_response, gui_chat_request, gui_chat_response).
Create dedicated "Live Interception & Manipulation" textbox (editable) and "Send Manipulated Message" button.
Implement send_manipulated_message() to send data back to proxy via WS with request_id.
Update append_to_activity_log to show more detailed events including manipulation flow.
Phase 4: Refinement & Robustness

Objective: Improve error handling, UI/UX, and data extraction.
Tasks:
4.1. Enhance JSON parsing and display in GUI (e.g., syntax highlighting if CustomTkinter allows, better error messages for invalid JSON).
4.2. Improve robustness of proxy shutdown.
4.3. Refine extract_info_from_request/response in db_logger.py for more accurate token counting and edge cases in LLM API responses.
4.4. Implement basic client-side JSON validation in the GUI's manipulation textbox before sending.
Future Enhancements (Post-MVP):

Key Tumbler Module: Re-introduce the sophisticated API key rotation/switching logic (rate limit, cost, error-driven) as originally discussed.
Log Filtering & Search: Add search, filter, and export capabilities to the GUI for the PostgreSQL logs.
Pre-defined Manipulation Rules: Allow users to define and apply common message manipulation rules (e.g., add a default system prompt, censor certain words).
Multiple LLM Provider Support: Streamline configuration and API key management for various LLM providers (OpenAI, Anthropic, Gemini, etc.).
Configuration UI: A more user-friendly interface within the GUI for managing config.py settings.
Prompt/Response Templating: Store and reuse common prompt templates.
# Logical Dependency Chain

This outlines the build order to achieve a working, visible front-end as quickly as possible, ensuring each piece is atomic and buildable.

Fundamental Setup:
1.1. config.py: Define all global parameters. This is the bedrock.
1.2. PostgreSQL DB Setup: Create database and messages_log table.
Database Core:
2.1. db_logger.py (initial functions): Enable database connection and basic logging (get_db_connection, log_message_to_db, placeholder extract_info functions).
Proxy Foundation:
3.1. proxy_server.py (basic HTTP proxy): Implement FastAPI app, /v1/{full_path:path} for basic interception, API key injection, forwarding, and integrate db_logger for request/response logging. Implement run_proxy_server().
3.2. proxy_server.py (/v1/gui_relay endpoint): Add the endpoint for the GUI chatbox to relay messages.
Initial GUI (First Visible Piece):
4.1. gui_app.py (basic structure): CustomTkinter window, Start/Stop Proxy buttons (calling run_proxy_server), basic Activity Log textbox, Chatbox UI (input/display/send).
4.2. GUI Chatbox Integration: Implement Messages() in gui_app.py to send messages to proxy_server.py's /v1/gui_relay endpoint.
(At this point, a user can start the proxy, see basic proxy events in a log, and use the integrated chatbox, which routes through the proxy and logs to DB. This is a first usable MVP before full manipulation.)
Live Manipulation Integration (Completing Core MVP):
5.1. proxy_server.py (WebSocket & Pausing Logic): Add /ws WebSocket endpoint, pending_requests dictionary, asyncio.Future for pausing, and modify the /v1/{full_path:path} interceptor to send to GUI and await manipulation.
5.2. gui_app.py (WebSocket Client & Manipulation UI): Implement WebSocket client connection and message receiving (_websocket_connect_and_listen, _process_gui_queue). Add "Live Interception & Manipulation" textbox (editable), "Send Manipulated Message" button. Implement send_manipulated_message() to send data back via WS.
Refinement & Production Readiness:
Error handling improvements, robust shutdown, better extraction in db_logger, UI polishing.
# Risks and Mitigations

1. Technical Challenges:

Risk: Maintaining stable WebSocket connection between the proxy and GUI for the pause-edit-resume flow.
Mitigation: Implement robust error handling, automatic reconnection logic for the WebSocket client in gui_app.py. Use websockets library which is generally stable.
Risk: User inputting invalid JSON in the manipulation textbox causing proxy errors.
Mitigation: Implement client-side JSON validation in gui_app.py before sending manipulated messages back to the proxy. Provide clear error messages to the user.
Risk: LLM provider API request timeout while a message is paused for manipulation in the GUI.
Mitigation: Implement a reasonable timeout (e.g., 60 seconds) on the proxy's asyncio.Future wait. If timed out, abort the request and notify the user through the GUI.
Risk: Performance overhead due to proxying and pausing, especially with many concurrent requests (unlikely for single-user OAPIE-Lite).
Mitigation: FastAPI's async nature and Python's threading for GUI/proxy separation should mitigate this for typical single-user development workflows. Monitor performance if scaling.
2. Figuring out the MVP that we can build upon:

Risk: Scope creep beyond "Lite" leading to an unfinishable initial version.
Mitigation: The defined roadmap strictly adheres to the core features. Future enhancements are explicitly deferred. The logical dependency chain prioritizes getting a working GUI with logging and chat before adding manipulation, then integrates manipulation.
3. Resource Constraints:

Risk: Limited developer time or expertise in specific technologies (e.g., CustomTkinter, async Python, PostgreSQL).
Mitigation: This detailed PRD serves as a comprehensive guide for an agentic AI coder (or a human developer), breaking down complexity. Leveraging established Python libraries minimizes the need for deep domain-specific knowledge in certain areas. Modular design allows for focused development on individual components.
# Appendix

Research Findings: The choice of FastAPI for the proxy and CustomTkinter for the GUI is based on their Python compatibility, ease of use for rapid development, and suitability for the defined architecture. WebSocket was selected for its real-time, bidirectional communication capabilities essential for the pause-edit-resume feature. PostgreSQL was chosen for its robustness and structured logging capabilities.
Technical Specifications: This PRD serves as the primary technical specification for the OAPIE-Lite MVP. Specific API details for LLM providers (like Gemini's generateContent or OpenAI's chat/completions API) are assumed to align with their official documentation.