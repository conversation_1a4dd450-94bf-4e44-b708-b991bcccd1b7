# Task ID: 30
# Title: Implement Integrated Chatbox Logic
# Status: pending
# Dependencies: 26, 29
# Priority: high
# Description: Implement the logic in `gui_app.py` for the integrated chatbox to construct an LLM API request payload from user input, send it to the proxy's `/v1/gui_relay` endpoint using `httpx`, and display the LLM response in the chat display textbox.
# Details:
In `gui_app.py`, implement the function triggered by the 'Send Chat' button or Enter key press in the input field. This function should get text from the input field, construct a JSON payload (e.g., `{'contents': [{'role': 'user', 'parts': [{'text': user_input}]}], 'model': CHAT_LLM_MODEL}` for Gemini format), send an async POST request to `http://localhost:8000/v1/gui_relay` using `httpx.AsyncClient` in a separate thread, wait for the response, extract the LLM's reply from the response body, and append both the user message and the LLM reply to the chat display textbox. Clear the input field after sending. Ensure robust error handling for network issues or LLM errors.

# Test Strategy:
Run the proxy and GUI. Type a message in the chatbox and click 'Send Chat'. Verify the message appears in the chat history, the request is sent to the proxy (check proxy logs/console), the proxy forwards it to the LLM, and the LLM's response is received and displayed in the chat history.
