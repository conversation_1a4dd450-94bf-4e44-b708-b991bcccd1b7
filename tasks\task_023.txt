# Task ID: 23
# Title: Setup PostgreSQL Database and Table
# Status: pending
# Dependencies: None
# Priority: high
# Description: Set up the local PostgreSQL database instance and create the `messages_log` table with the schema defined in the PRD.
# Details:
Ensure PostgreSQL is running. Connect to the database server (e.g., using psql or pgAdmin).
Execute SQL commands to create the database 'oapie_logs_db' if it doesn't exist.
Execute SQL to create the 'messages_log' table:
CREATE TABLE messages_log (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    direction VARCHAR(10),
    message_type VARCHAR(10),
    llm_model VARCHAR(50),
    request_path TEXT,
    status_code INTEGER,
    prompt_text TEXT,
    response_text TEXT,
    token_count_in INTEGER,
    token_count_out INTEGER,
    full_headers JSONB,
    full_body JSONB
);

# Test Strategy:
Verify the database and the `messages_log` table exist and have the correct schema using a PostgreSQL client.
