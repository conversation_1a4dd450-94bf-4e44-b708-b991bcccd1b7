# Task ID: 27
# Title: Implement Core HTTP Proxy Interception and Logging
# Status: pending
# Dependencies: 24, 25, 26
# Priority: high
# Description: Implement the core HTTP proxy endpoint (`/v1/{full_path:path}`) in `proxy_server.py` to intercept incoming requests, inject the API key, forward the request to the actual LLM provider using `httpx`, receive the response, and log both request and response using `db_logger.py`.
# Details:
In `proxy_server.py`, define an async endpoint `async def proxy_llm_request(full_path: str, request: Request):`. This endpoint should handle POST requests. Extract the request body and headers. Modify headers to include the LLM API key (e.g., `Authorization: Bearer {config.LLM_API_KEY}`). Construct the target LLM API URL using `config.TARGET_LLM_BASE_URL` and `full_path`. Use `httpx.AsyncClient` to send the POST request to the LLM provider. Log the incoming request details using `db_logger.log_message_to_db` and `db_logger.extract_info_from_request`. Wait for the LLM response. Log the outgoing response details using `db_logger.log_message_to_db` and `db_logger.extract_info_from_response`. Return the LLM response back to the original client.

# Test Strategy:
Run the proxy. Configure a client application (like `curl` or a simple script) to send an LLM API request (e.g., to `http://localhost:8000/v1/chat/completions`). Verify the request is forwarded to the actual LLM provider, the correct response is received by the client, and both the request and response are logged correctly in the PostgreSQL database.
