"""
<PERSON><PERSON><PERSON> to run the OAPIE-Lite proxy server.

This script imports and runs the proxy server from src/proxy_server.py.
"""

import sys
import os
import traceback

# Configure Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, script_dir)

# Set up basic logging
import logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    try:
        # Check if .env file exists and is valid
        env_path = os.path.join(script_dir, '.env')
        if not os.path.exists(env_path):
            print("Warning: .env file not found. Creating a default one...")
            with open(env_path, 'w') as f:
                f.write("""# LLM API Keys
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
MISTRAL_API_KEY=your_mistral_api_key_here

# LLM Configuration
LLM_API_BASE_URL=https://api.openai.com/v1
CHAT_LLM_MODEL=gpt-3.5-turbo

# Proxy Server Configuration
PROXY_HOST=127.0.0.1
PROXY_PORT=8000

# WebSocket Configuration
WS_HOST=127.0.0.1
WS_PORT=8000

# Database Configuration
USE_SQLITE=True
SQLITE_DB_PATH=oapie_logs.db
DEBUG_MODE=True
LOG_LEVEL=INFO
""")
        
        # Import the run_proxy_server function
        from src.proxy_server import run_proxy_server
        
        # Start the proxy server
        print("Starting OAPIE-Lite proxy server...")
        run_proxy_server()
        
    except ImportError as e:
        print(f"Error importing required modules: {e}")
        print("Please make sure all dependencies are installed:")
        print("pip install -r requirements.txt")
        traceback.print_exc()
    except Exception as e:
        print(f"Error starting OAPIE-Lite proxy server: {e}")
        traceback.print_exc()
    finally:
        print("\nPress Enter to exit...")
        input()

if __name__ == "__main__":
    main()