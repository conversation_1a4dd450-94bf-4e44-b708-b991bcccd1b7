# Task ID: 36
# Title: Implement Proxy WebSocket Handling for Manipulated Messages
# Status: pending
# Dependencies: 33, 35
# Priority: high
# Description: Implement WebSocket message handling in `proxy_server.py` to receive `manipulated_message` messages from the GUI, retrieve the corresponding paused request using the `request_id`, update its body with the manipulated content, and resume the request by setting the result of the associated `asyncio.Future`.
# Details:
In the WebSocket endpoint handler in `proxy_server.py`, add logic to process messages with `type: 'manipulated_message'`. Extract the `request_id` and `body`. Look up the stored request context and `asyncio.Future` using the `request_id`. Update the request body in the stored context. Set the result of the `asyncio.Future` to signal the waiting `catchall` endpoint to resume processing with the new body. Handle cases where the `request_id` is not found (e.g., timeout occurred).

# Test Strategy:
Run the proxy and GUI. Send an LLM request. When the JSON appears in the GUI, edit it and click 'Send Manipulated Message'. Verify the proxy receives the manipulated message, updates the request body, and the request successfully proceeds to the LLM provider with the *modified* body. Verify the LLM response is received by the original client and logged correctly.
