# Task ID: 25
# Title: Implement Initial Data Extraction Functions
# Status: pending
# Dependencies: 24
# Priority: medium
# Description: Implement initial placeholder functions (`extract_info_from_request`, `extract_info_from_response`) in `db_logger.py` to extract key details from LLM API request and response bodies.
# Details:
In `db_logger.py`, add `extract_info_from_request(body)` and `extract_info_from_response(body)` functions. Initially, these can be basic, e.g., returning the full body or simple placeholders. They should be designed to parse common LLM API JSON structures (like OpenAI's chat completions or Gemini's generateContent) to find the main prompt/response text, model name, and potentially estimate tokens.

# Test Strategy:
Write unit tests for the extraction functions using sample LLM request and response JSON payloads to ensure they correctly identify and return the required fields (model, prompt, response, etc.).
