# OAPIE-Lite

OAPIE-Lite is a desktop application that functions as a Man-in-the-Middle (MITM) proxy for intercepting, manipulating, and relaying LLM API messages.

## Features

- Intercept LLM API calls
- Live message manipulation
- Persistent logging to a database (PostgreSQL or SQLite)
- Desktop GUI with integrated chatbox
- WebSocket communication for real-time updates

## Installation

1. Clone the repository
2. Create a virtual environment:
   ```
   python -m venv venv
   ```
3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Unix/MacOS: `source venv/bin/activate`
4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
5. Copy `.env.example` to `.env` and configure your API keys and settings

## Running the Application

### Option 1: Start the GUI (Recommended)

Run the GUI application, which will allow you to start/stop the proxy server:

```
python run_gui.py
```

### Option 2: Start the Proxy Server Directly

If you only want to run the proxy server without the GUI:

```
python run_proxy_server.py
```

## Usage

1. Start the application using one of the methods above
2. If using the GUI, click the "Start Proxy" button to start the proxy server
3. Configure your LLM client to use `http://localhost:8000/` as the API base URL
4. Send requests from your LLM client
5. Intercepted requests will appear in the GUI for manipulation
6. Edit the request JSON as needed and click "Send Manipulated Message"
7. The modified request will be forwarded to the LLM provider

## Troubleshooting

- If the application fails to start, check the console output for error messages
- Ensure all required dependencies are installed
- Verify that the ports specified in the configuration are available
- Check that your API keys are correctly configured in the `.env` file

## License

MIT