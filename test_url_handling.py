#!/usr/bin/env python3
"""
Test script to verify URL handling in the proxy server.

This script tests that URLs with encoded characters (like %3A for :) 
are properly handled by the proxy server.
"""

import sys
import os
import asyncio
from urllib.parse import quote, unquote

# Add the current directory to the path so we can import from src
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_url_encoding():
    """Test URL encoding and decoding scenarios."""
    
    print("=== URL Encoding/Decoding Tests ===\n")
    
    # Test case 1: Original Gemini URL
    original_url = "/v1beta/models/gemini-2.5-flash-preview-04-17:streamGenerateContent?alt=sse"
    encoded_url = "/v1beta/models/gemini-2.5-flash-preview-04-17%3AstreamGenerateContent?alt=sse"
    
    print(f"Original URL: {original_url}")
    print(f"Encoded URL:  {encoded_url}")
    print(f"Decoded back: {unquote(encoded_url)}")
    print(f"Match: {original_url == unquote(encoded_url)}")
    print()
    
    # Test case 2: URL encoding/decoding
    test_paths = [
        "v1beta/models/gemini-2.5-flash-preview-04-17:streamGenerateContent",
        "v1/chat/completions",
        "v1/models/gpt-4:generate",
        "api/v1/models/claude-3:complete"
    ]
    
    print("Testing various URL paths:")
    for path in test_paths:
        encoded = quote(path, safe='/')  # Keep slashes unencoded
        decoded = unquote(encoded)
        print(f"  Original: {path}")
        print(f"  Encoded:  {encoded}")
        print(f"  Decoded:  {decoded}")
        print(f"  Match:    {path == decoded}")
        print()
    
    return True

def test_fastapi_url_handling():
    """Test how FastAPI handles URL parameters."""
    
    print("=== FastAPI URL Parameter Handling ===\n")
    
    # Simulate what FastAPI does with path parameters
    test_cases = [
        {
            "full_path": "v1beta/models/gemini-2.5-flash-preview-04-17:streamGenerateContent",
            "query": "alt=sse",
            "expected_target": "v1beta/models/gemini-2.5-flash-preview-04-17:streamGenerateContent?alt=sse"
        },
        {
            "full_path": "v1/chat/completions",
            "query": "",
            "expected_target": "v1/chat/completions"
        },
        {
            "full_path": "v1/models/gpt-4:generate",
            "query": "stream=true&temperature=0.7",
            "expected_target": "v1/models/gpt-4:generate?stream=true&temperature=0.7"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"Test Case {i}:")
        print(f"  Full Path: {case['full_path']}")
        print(f"  Query:     {case['query']}")
        
        # Simulate constructing the full URL
        if case['query']:
            full_url = f"{case['full_path']}?{case['query']}"
        else:
            full_url = case['full_path']
            
        print(f"  Result:    {full_url}")
        print(f"  Expected:  {case['expected_target']}")
        print(f"  Match:     {full_url == case['expected_target']}")
        print()
    
    return True

def simulate_proxy_url_construction():
    """Simulate how the proxy server constructs target URLs."""
    
    print("=== Proxy Server URL Construction Simulation ===\n")
    
    # Simulate the proxy server logic
    base_url = "https://generativelanguage.googleapis.com"
    
    test_requests = [
        {
            "request_url": "http://localhost:8000/v1beta/models/gemini-2.5-flash-preview-04-17:streamGenerateContent?alt=sse",
            "base_url": "http://localhost:8000/",
            "expected_path": "v1beta/models/gemini-2.5-flash-preview-04-17:streamGenerateContent?alt=sse"
        },
        {
            "request_url": "http://localhost:8000/v1/chat/completions",
            "base_url": "http://localhost:8000/",
            "expected_path": "v1/chat/completions"
        }
    ]
    
    for i, req in enumerate(test_requests, 1):
        print(f"Test Request {i}:")
        print(f"  Request URL: {req['request_url']}")
        print(f"  Base URL:    {req['base_url']}")
        
        # Simulate the proxy logic
        full_url_with_query = req['request_url'].replace(req['base_url'], "")
        if full_url_with_query.startswith("/"):
            full_url_with_query = full_url_with_query[1:]
            
        target_url = f"{base_url}/{full_url_with_query}"
        
        print(f"  Extracted:   {full_url_with_query}")
        print(f"  Target URL:  {target_url}")
        print(f"  Expected:    {req['expected_path']}")
        print(f"  Match:       {full_url_with_query == req['expected_path']}")
        print()
    
    return True

def main():
    """Run all URL handling tests."""
    
    print("🧪 Testing URL Handling for OAPIE-Lite Proxy Server\n")
    
    success = True
    
    try:
        success &= test_url_encoding()
        success &= test_fastapi_url_handling()
        success &= simulate_proxy_url_construction()
        
        if success:
            print("✅ All URL handling tests passed!")
            print("\nThe proxy server should now correctly handle URLs with encoded characters.")
            print("Try your Gemini request again - it should work now!")
        else:
            print("❌ Some tests failed. Check the output above for details.")
            
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        success = False
    
    return success

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
