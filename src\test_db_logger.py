"""
Test script for the db_logger module.

This script tests the database connection and logging functionality.
"""

import sys
import json
from datetime import datetime

# Add the parent directory to the path so we can import from src
sys.path.append('..')

from src.db_logger import get_db_connection, log_message_to_db, query_logs

def test_db_connection():
    """Test database connection"""
    print("Testing database connection...")
    try:
        conn = get_db_connection()
        print("✓ Database connection successful")
        if hasattr(conn, 'close') and callable(getattr(conn, 'close')):
            conn.close()
        return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def test_log_message():
    """Test logging a message to the database"""
    print("\nTesting message logging...")
    
    # Sample request data
    test_data = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "temperature": 0.7
    }
    
    # Log a test message
    success = log_message_to_db(
        direction="request",
        message_type="test",
        data=test_data,
        path="/v1/chat/completions",
        model="gpt-3.5-turbo",
        prompt="system: You are a helpful assistant.\nuser: Hello, how are you?",
        tokens=20
    )
    
    if success:
        print("✓ Message logged successfully")
    else:
        print("✗ Failed to log message")
    
    return success

def test_query_logs():
    """Test querying logs from the database"""
    print("\nTesting log querying...")
    
    logs = query_logs(limit=5)
    
    if logs:
        print(f"✓ Retrieved {len(logs)} log entries")
        print("\nMost recent log entry:")
        for key, value in logs[0].items():
            if key == 'raw_data':
                print(f"  {key}: {json.dumps(value)[:100]}...")
            else:
                print(f"  {key}: {value}")
    else:
        print("✗ No log entries found or query failed")
    
    return len(logs) > 0

def main():
    """Run all tests"""
    print("=== DB Logger Module Tests ===")
    print(f"Timestamp: {datetime.now().isoformat()}")
    print("===========================\n")
    
    connection_ok = test_db_connection()
    if not connection_ok:
        print("\n❌ Database connection failed. Aborting remaining tests.")
        return False
    
    log_ok = test_log_message()
    query_ok = test_query_logs()
    
    print("\n=== Test Summary ===")
    print(f"Database Connection: {'✓' if connection_ok else '✗'}")
    print(f"Message Logging: {'✓' if log_ok else '✗'}")
    print(f"Log Querying: {'✓' if query_ok else '✗'}")
    print("===================")
    
    return connection_ok and log_ok and query_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)