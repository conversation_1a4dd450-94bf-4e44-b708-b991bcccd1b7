# Task ID: 22
# Title: Define Configuration File (config.py)
# Status: pending
# Dependencies: None
# Priority: high
# Description: Define and populate the central configuration file (config.py) with settings for LLM API key, proxy port, WebSocket endpoint, and PostgreSQL database credentials.
# Details:
Create a config.py file. Define variables for:
TARGET_LLM_BASE_URL = 'https://generativelanguage.googleapis.com' # Or 'https://api.openai.com' for OpenAI
LLM_API_KEY = 'your_llm_api_key' # IMPORTANT: Replace with your actual LLM API key
CHAT_LLM_MODEL = 'gemini-pro' # Example, adjust based on LLM provider capabilities
PROXY_HOST = '127.0.0.1'
PROXY_PORT = 8000
WEBSOCKET_URL = f'ws://{PROXY_HOST}:{PROXY_PORT}/ws'
GUI_PROXY_RELAY_ENDPOINT = f'http://{PROXY_HOST}:{PROXY_PORT}/v1/gui_relay'
DB_HOST = 'localhost'
DB_PORT = 5432
DB_NAME = 'oapie_logs_db'
DB_USER = 'your_db_user'
DB_PASSWORD = 'your_db_password'

# Test Strategy:
Ensure config.py is created with all required variables defined and accessible.
