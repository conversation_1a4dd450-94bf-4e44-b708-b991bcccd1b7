"""
Database Initialization Script for OAPIE-Lite.

This script sets up the required database and tables for OAPIE-Lite.
It supports both PostgreSQL and SQLite databases.
"""

import os
import sys
import logging
import argparse

import psycopg2
import sqlite3

from src.config import (
    DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD,
    USE_SQLITE, SQLITE_DB_PATH
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# SQL statements for table creation
POSTGRES_CREATE_DB_SQL = f"CREATE DATABASE {DB_NAME};"

POSTGRES_CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS messages_log (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    direction TEXT NOT NULL,
    message_type TEXT NOT NULL,
    path TEXT,
    model TEXT,
    prompt TEXT,
    response TEXT,
    tokens INTEGER,
    status_code INTEGER,
    raw_data JSONB
);
"""

SQLITE_CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS messages_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    direction TEXT NOT NULL,
    message_type TEXT NOT NULL,
    path TEXT,
    model TEXT,
    prompt TEXT,
    response TEXT,
    tokens INTEGER,
    status_code INTEGER,
    raw_data TEXT
);
"""

def init_sqlite():
    """Initialize SQLite database"""
    try:
        conn = sqlite3.connect(SQLITE_DB_PATH)
        conn.execute(SQLITE_CREATE_TABLE_SQL)
        conn.commit()
        logger.info(f"SQLite database initialized successfully at {SQLITE_DB_PATH}")
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error initializing SQLite database: {e}")
        return False

def init_postgres():
    """Initialize PostgreSQL database"""
    try:
        # First connect to default 'postgres' database to create our database
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname="postgres",
            user=DB_USER,
            password=DB_PASSWORD
        )
        conn.autocommit = True  # Required for creating database
        
        # Check if database already exists
        with conn.cursor() as cursor:
            cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (DB_NAME,))
            exists = cursor.fetchone()
            
            if not exists:
                cursor.execute(POSTGRES_CREATE_DB_SQL)
                logger.info(f"Created PostgreSQL database: {DB_NAME}")
            else:
                logger.info(f"PostgreSQL database {DB_NAME} already exists")
        
        conn.close()
        
        # Now connect to our database and create tables
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        
        with conn.cursor() as cursor:
            cursor.execute(POSTGRES_CREATE_TABLE_SQL)
            conn.commit()
            
        logger.info(f"PostgreSQL tables initialized successfully in {DB_NAME}")
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error initializing PostgreSQL database: {e}")
        return False

def main():
    """Main function to initialize the database"""
    parser = argparse.ArgumentParser(description="Initialize OAPIE-Lite database")
    parser.add_argument("--force-sqlite", action="store_true", help="Force SQLite initialization")
    parser.add_argument("--force-postgres", action="store_true", help="Force PostgreSQL initialization")
    args = parser.parse_args()
    
    if args.force_sqlite:
        return init_sqlite()
    elif args.force_postgres:
        return init_postgres()
    else:
        # Use configuration setting
        if USE_SQLITE:
            return init_sqlite()
        else:
            return init_postgres()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)