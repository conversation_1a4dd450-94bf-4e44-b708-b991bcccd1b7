# Task ID: 33
# Title: Enhance Proxy Interceptor for Pausing and Sending to GUI
# Status: pending
# Dependencies: 27, 31
# Priority: high
# Description: Enhance the core HTTP proxy interceptor (`/v1/{full_path:path}`) in `proxy_server.py` to pause the request processing, assign a unique request ID, store the request context, and send the request body and ID to the GUI via WebSocket.
# Details:
In the `catchall` endpoint in `proxy_server.py`, before forwarding the request: Generate a unique `request_id` (e.g., using `uuid.uuid4()`). Store the request object or necessary context (headers, original URL, etc.) in a dictionary keyed by `request_id`. Create an `asyncio.Future` associated with this `request_id`. Send a JSON message via WebSocket to the GUI containing `type: 'intercepted_request'`, `request_id`, and the request `body`. Await the result of the `asyncio.Future`. The request processing will pause here until the Future is set by the WebSocket handler receiving the manipulated message.

# Test Strategy:
Run the proxy and GUI. Send an LLM request from a client. Verify the request is paused (it shouldn't immediately reach the LLM or return a response). Verify a message with `type: 'intercepted_request'`, `request_id`, and the request body is sent from the proxy to the GUI via WebSocket (check logs).
