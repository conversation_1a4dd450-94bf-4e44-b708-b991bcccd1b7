# Task ID: 31
# Title: Implement WebSocket Communication Setup
# Status: pending
# Dependencies: 26, 28
# Priority: high
# Description: Implement the WebSocket endpoint (`/ws`) in `proxy_server.py` and the WebSocket client connection and basic message receiving loop in `gui_app.py`.
# Details:
In `proxy_server.py`, add a WebSocket endpoint `async def websocket_endpoint(websocket: WebSocket):`. Accept the connection. Implement a loop to receive messages. In `gui_app.py`, implement an async function `_websocket_connect_and_listen()` that connects to `config.WEBSOCKET_URL` using `websockets.connect`. This function should run in a separate thread (with its own asyncio loop) managed by the GUI. Implement a loop within this function to receive messages from the WebSocket and put them into a `queue.Queue()` for the GUI's main thread to process (`_process_gui_queue`). Ensure connection retry logic in the GUI.

# Test Strategy:
Run the proxy and GUI. Verify the WebSocket connection is established (check proxy/GUI logs). Send a test message from the proxy to the GUI via WS and verify the GUI receives it (e.g., prints to console or activity log). Disconnect/reconnect WS to test retry logic.
