# Task ID: 35
# Title: Implement GUI Logic to Send Manipulated Message
# Status: pending
# Dependencies: 32, 34
# Priority: high
# Description: Implement the logic for the 'Send Manipulated Message' button in `gui_app.py` to get the edited JSON from the manipulation textbox, construct a `manipulated_message` WebSocket message including the stored `request_id`, and send it back to the proxy.
# Details:
In `gui_app.py`, implement the function triggered by the 'Send Manipulated Message' button. Retrieve the text content from the 'Live Interception & Manipulation' textbox. Attempt to parse it as JSON. Retrieve the stored `self.current_request_id`. Construct a JSON message: `{'type': 'manipulated_message', 'request_id': self.current_request_id, 'manipulated_body': parsed_json_object}`. Send this JSON message to the proxy via the WebSocket connection using `asyncio.run_coroutine_threadsafe(self.websocket_client.send_text(...), self.websocket_loop)`. Clear the manipulation textbox, set its state back to `disabled`, disable the 'Send Manipulated Message' button, and clear `self.current_request_id`. Provide error feedback if JSON parsing fails.

# Test Strategy:
Run the proxy and GUI. Send an LLM request. When the JSON appears in the GUI, edit it (both valid and invalid JSON to test error handling). Click 'Send Manipulated Message'. Verify a message with `type: 'manipulated_message'`, the correct `request_id`, and the edited body is sent from the GUI to the proxy via WebSocket (check proxy logs). Verify GUI elements (textbox, button) are disabled after sending.
