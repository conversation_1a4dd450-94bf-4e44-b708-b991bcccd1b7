# Task ID: 28
# Title: Implement Basic GUI Structure and Proxy Control
# Status: pending
# Dependencies: 21, 22
# Priority: high
# Description: Implement the basic CustomTkinter GUI structure in `gui_app.py`, including the main window, 'Start Proxy' and 'Stop Proxy' buttons, and an 'Activity Log' textbox.
# Details:
Create `gui_app.py`. Initialize `customtkinter.CTk()` window. Create `CTkButton` for 'Start Proxy' and 'Stop Proxy'. Implement `start_proxy()` and `stop_proxy()` functions. `start_proxy()` should run `proxy_server.run_proxy_server()` in a separate daemon thread (to keep the GUI responsive) and then attempt to establish the WebSocket connection. `stop_proxy()` should gracefully close the WebSocket client connection before updating the proxy status and enabling the start button. Create a `CTkTextbox` for the 'Activity Log' and a helper function `append_to_activity_log(message)` to add text to it.

# Test Strategy:
Run `gui_app.py`. Verify the window appears with the correct buttons and textbox. Click 'Start Proxy' and verify the proxy server starts (check console output or a health check) and the GUI status updates to 'Running' and 'Connected'. Click 'Stop Proxy' and verify the proxy server attempts to shut down and the GUI status updates to 'Stopped'.

# Subtasks:
## 1. Set up CustomTkinter Window [pending]
### Dependencies: None
### Description: Create the main application window using CustomTkinter, configure basic window properties (title, size, theme).
### Details:
Initialize the CTk root window, set title, initial dimensions, and appearance mode/color theme.

## 2. Add UI Elements to Window [pending]
### Dependencies: 28.1
### Description: Place all necessary widgets onto the window: labels for input fields, entry fields for configuration (e.g., port), Start/Stop buttons, and a textbox for the activity log.
### Details:
Use CTkLabel, CTkEntry, CTkButton, and CTkTextbox. Arrange elements using pack, grid, or place geometry managers.

## 3. Implement Basic Button Actions [pending]
### Dependencies: 28.2
### Description: Write the initial callback functions for the Start and Stop buttons. These functions will contain the logic to initiate or terminate the proxy process.
### Details:
Define functions like `start_proxy_action()` and `stop_proxy_action()`. Initially, these might just print status or change button states.

## 4. Integrate Threading for Proxy Control [pending]
### Dependencies: 28.3
### Description: Modify the button actions to run the proxy server logic in a separate thread. This prevents the GUI from freezing while the proxy is running.
### Details:
Use Python's `threading` module. Create a thread for the proxy server process and manage its start/stop lifecycle from the GUI actions.

## 5. Implement Activity Log Functionality [pending]
### Dependencies: 28.2, 28.4
### Description: Add functionality to update the activity log textbox with messages indicating proxy status, events, or errors. Ensure thread-safe updates if messages originate from the proxy thread.
### Details:
Create a method to insert text into the log textbox. Implement a mechanism (e.g., queue, polling, or thread-safe method) to receive messages from the proxy thread and display them in the log.

