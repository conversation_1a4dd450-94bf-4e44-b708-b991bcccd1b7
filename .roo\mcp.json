{"mcpServers": {"taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"PERPLEXITY_API_KEY": "pplx-wXQFgHaRsPGGxUjOYODX2ISiwC8Rljl6ckzEPXhixzYdyFxwYOUR_PERPLEXITY_API_KEY_HERE", "GOOGLE_API_KEY": "AIzaSyAOi3EFxTDuYsDd4GOIQ0HAmbSNxqTkXTs", "OPENROUTER_API_KEY": "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", "XAI_API_KEY": "********************************************************"}, "type": "stdio", "disabled": false, "alwaysAllow": ["get_task"]}, "github.com/pashpashpash/perplexity-mcp": {"command": "node", "args": ["C:\\Users\\<USER>\\Documents\\Cline\\MCP\\github.com\\pashpashpash\\perplexity-mcp\\build\\index.js"], "env": {"PERPLEXITY_API_KEY": "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"}, "disabled": false, "autoApprove": [], "alwaysAllow": ["search", "get_documentation", "find_apis", "check_deprecated_code", "chat_perplexity"]}, "vertex-ai-mcp-server": {"command": "node", "args": ["D:\\AI\\MCP-SERVERS\\vertex-ai-mcp-server\\build\\index.js"], "env": {"AI_PROVIDER": "gemini", "GEMINI_API_KEY": "AIzaSyAb6a33S_NynHvjxogbU7y1HRRgSpy5iqQ", "GEMINI_MODEL_ID": "gemini-2.5-flash-preview-05-20"}, "transportType": "stdio", "disabled": false, "alwaysAllow": ["explain_topic_with_docs", "get_doc_snippets"], "timeout": 3600}}}