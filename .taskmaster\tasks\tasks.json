{"master": {"tasks": [{"id": 21, "title": "Install Python Dependencies", "description": "Install all necessary Python packages as specified in the PRD (fastapi, uvicorn, httpx, customtkinter, psycopg2-binary, websockets).", "details": "Use pip to install the required packages. Create a requirements.txt file for reproducibility.\npip install fastapi uvicorn httpx customtkinter psycopg2-binary websockets", "testStrategy": "Verify that all packages are installed correctly by attempting to import them in a Python interpreter.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Create requirements.txt", "description": "Generate the requirements.txt file listing all necessary Python packages for the project.", "dependencies": [], "details": "Create a file named 'requirements.txt' in the project's root directory. List each required Python package and its version (if necessary) on a new line.", "status": "pending"}, {"id": 2, "title": "Run pip install", "description": "Execute the pip command to install all packages listed in the requirements.txt file.", "dependencies": [1], "details": "Open a terminal or command prompt in the project's root directory and run the command: `pip install -r requirements.txt`. Ensure you are in the correct virtual environment if one is being used.", "status": "pending"}]}, {"id": 22, "title": "Define Configuration File (config.py)", "description": "Define and populate the central configuration file (config.py) with settings for LLM API key, proxy port, WebSocket endpoint, and PostgreSQL database credentials.", "details": "Create a config.py file. Define variables for:\nTARGET_LLM_BASE_URL = 'https://generativelanguage.googleapis.com' # Or 'https://api.openai.com' for OpenAI\nLLM_API_KEY = 'your_llm_api_key' # IMPORTANT: Replace with your actual LLM API key\nCHAT_LLM_MODEL = 'gemini-pro' # Example, adjust based on LLM provider capabilities\nPROXY_HOST = '127.0.0.1'\nPROXY_PORT = 8000\nWEBSOCKET_URL = f'ws://{PROXY_HOST}:{PROXY_PORT}/ws'\nGUI_PROXY_RELAY_ENDPOINT = f'http://{PROXY_HOST}:{PROXY_PORT}/v1/gui_relay'\nDB_HOST = 'localhost'\nDB_PORT = 5432\nDB_NAME = 'oapie_logs_db'\nDB_USER = 'your_db_user'\nDB_PASSWORD = 'your_db_password'", "testStrategy": "Ensure config.py is created with all required variables defined and accessible.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 23, "title": "Setup PostgreSQL Database and Table", "description": "Set up the local PostgreSQL database instance and create the `messages_log` table with the schema defined in the PRD.", "details": "Ensure PostgreSQL is running. Connect to the database server (e.g., using psql or pgAdmin).\nExecute SQL commands to create the database 'oapie_logs_db' if it doesn't exist.\nExecute SQL to create the 'messages_log' table:\nCREATE TABLE messages_log (\n    id SERIAL PRIMARY KEY,\n    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,\n    direction VARCHAR(10),\n    message_type VARCHAR(10),\n    llm_model VARCHAR(50),\n    request_path TEXT,\n    status_code INTEGER,\n    prompt_text TEXT,\n    response_text TEXT,\n    token_count_in INTEGER,\n    token_count_out INTEGER,\n    full_headers JSONB,\n    full_body JSONB\n);", "testStrategy": "Verify the database and the `messages_log` table exist and have the correct schema using a PostgreSQL client.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 24, "title": "Implement Database Logger Module (db_logger.py)", "description": "Implement the `db_logger.py` module including functions for database connection (`get_db_connection`) and logging messages (`log_message_to_db`), using `psycopg2-binary`.", "details": "Create `db_logger.py`. Implement `get_db_connection()` using `psycopg2.connect` with credentials from `config.py`. Implement `log_message_to_db(data)` function that takes a dictionary of log data, connects to the DB, and inserts a row into the `messages_log` table using a parameterized SQL query to prevent injection.", "testStrategy": "Write a simple script to call `get_db_connection()` and `log_message_to_db()` with test data. Verify a new row appears in the `messages_log` table in the database.", "priority": "high", "dependencies": [23], "status": "pending", "subtasks": []}, {"id": 25, "title": "Implement Initial Data Extraction Functions", "description": "Implement initial placeholder functions (`extract_info_from_request`, `extract_info_from_response`) in `db_logger.py` to extract key details from LLM API request and response bodies.", "details": "In `db_logger.py`, add `extract_info_from_request(path, headers, body)` and `extract_info_from_response(path, headers, body, status_code)` functions. These should parse common LLM API JSON structures (like OpenAI's chat completions or Gemini's generateContent) to find the main prompt/response text, model name, and potentially estimate tokens. Ensure they handle cases where fields might be missing gracefully.", "testStrategy": "Write unit tests for the extraction functions using sample LLM request and response JSON payloads to ensure they correctly identify and return the required fields (model, prompt, response, etc.).", "priority": "medium", "dependencies": [24], "status": "pending", "subtasks": []}, {"id": 26, "title": "Setup Proxy Server Basic Structure and GUI Relay Endpoint", "description": "Set up the basic FastAPI application structure in `proxy_server.py`, configure <PERSON><PERSON><PERSON>, add a health check endpoint (`/health`), and implement the `/v1/gui_relay` endpoint for the GUI chatbox.", "details": "Create `proxy_server.py`. Initialize `FastAPI()` app. Define an async function `run_proxy_server()` that uses `uvicorn.run()` to start the app on `config.PROXY_HOST` and `config.PROXY_PORT`. Add `log_level=\"warning\"` to `uvicorn.run()` to suppress verbose logs. Add a simple GET endpoint `/health` that returns `{'status': 'ok'}`. Implement an async POST endpoint `/v1/gui_relay` that accepts a JSON body (representing a chat request from the GUI). This endpoint will initially just log the received body or return a placeholder response.", "testStrategy": "Run `proxy_server.py`. Use `curl` or a browser to access `/health` and verify the response. Use `curl` or a tool like Postman to send a POST request to `/v1/gui_relay` with a JSON body and verify the server receives it (e.g., via console print).", "priority": "high", "dependencies": [22], "status": "pending", "subtasks": [{"id": 1, "title": "Initialize FastAPI Application", "description": "Create the main FastAPI application instance and set up the basic project structure.", "dependencies": [], "details": "Set up the project directory, create the main application file (e.g., main.py), and instantiate the FastAPI app object.", "status": "pending"}, {"id": 2, "title": "Configure <PERSON><PERSON><PERSON>", "description": "Set up <PERSON>vic<PERSON> as the ASGI server to run the FastAPI application.", "dependencies": [1], "details": "Add Uvicorn to project dependencies and configure how to run the application using Uvicorn (e.g., via a command or script). Ensure `log_level=\"warning\"` is set for cleaner output.", "status": "pending"}, {"id": 3, "title": "Implement Health Check Endpoint", "description": "Add a simple endpoint to verify the server is running.", "dependencies": [1], "details": "Create a GET endpoint (e.g., `/health`) that returns a basic success response, like a JSON object `{\"status\": \"ok\"}`.", "status": "pending"}, {"id": 4, "title": "Implement Basic GUI Relay Endpoint", "description": "Create the initial endpoint structure for relaying data from the GUI.", "dependencies": [1], "details": "Define the endpoint path and method (e.g., POST `/gui/relay`) that will receive data from the GUI. Implement basic request handling, potentially just logging the received data or returning a placeholder response.", "status": "pending"}]}, {"id": 27, "title": "Implement Core HTTP Proxy Interception and Logging", "description": "Implement the core HTTP proxy endpoint (`/v1/{full_path:path}`) in `proxy_server.py` to intercept incoming requests, inject the API key, forward the request to the actual LLM provider using `httpx`, receive the response, and log both request and response using `db_logger.py`.", "details": "In `proxy_server.py`, define an async endpoint `async def proxy_llm_request(full_path: str, request: Request):`. This endpoint should handle POST requests. Extract the request body and headers. Modify headers to include the LLM API key (e.g., `Authorization: Bearer {config.LLM_API_KEY}`). Construct the target LLM API URL using `config.TARGET_LLM_BASE_URL` and `full_path`. Use `httpx.AsyncClient` to send the POST request to the LLM provider. Log the incoming request details using `db_logger.log_message_to_db` and `db_logger.extract_info_from_request`. Wait for the LLM response. Log the outgoing response details using `db_logger.log_message_to_db` and `db_logger.extract_info_from_response`. Return the LLM response back to the original client.", "testStrategy": "Run the proxy. Configure a client application (like `curl` or a simple script) to send an LLM API request (e.g., to `http://localhost:8000/v1/chat/completions`). Verify the request is forwarded to the actual LLM provider, the correct response is received by the client, and both the request and response are logged correctly in the PostgreSQL database.", "priority": "high", "dependencies": [24, 25, 26], "status": "pending", "subtasks": []}, {"id": 28, "title": "Implement Basic GUI Structure and Proxy Control", "description": "Implement the basic CustomTkinter GUI structure in `gui_app.py`, including the main window, 'Start Proxy' and 'Stop Proxy' buttons, and an 'Activity Log' textbox.", "details": "Create `gui_app.py`. Initialize `customtkinter.CTk()` window. Create `CTkButton` for 'Start Proxy' and 'Stop Proxy'. Implement `start_proxy()` and `stop_proxy()` functions. `start_proxy()` should run `proxy_server.run_proxy_server()` in a separate daemon thread (to keep the GUI responsive) and then attempt to establish the WebSocket connection. `stop_proxy()` should gracefully close the WebSocket client connection before updating the proxy status and enabling the start button. Create a `CTkTextbox` for the 'Activity Log' and a helper function `append_to_activity_log(message)` to add text to it.", "testStrategy": "Run `gui_app.py`. Verify the window appears with the correct buttons and textbox. Click 'Start Proxy' and verify the proxy server starts (check console output or a health check) and the GUI status updates to 'Running' and 'Connected'. Click 'Stop Proxy' and verify the proxy server attempts to shut down and the GUI status updates to 'Stopped'.", "priority": "high", "dependencies": [21, 22], "status": "pending", "subtasks": [{"id": 1, "title": "Set up CustomTkinter Window", "description": "Create the main application window using CustomTkinter, configure basic window properties (title, size, theme).", "dependencies": [], "details": "Initialize the CTk root window, set title, initial dimensions, and appearance mode/color theme.", "status": "pending"}, {"id": 2, "title": "Add UI Elements to Window", "description": "Place all necessary widgets onto the window: labels for input fields, entry fields for configuration (e.g., port), Start/Stop buttons, and a textbox for the activity log.", "dependencies": [1], "details": "Use CTkLabel, CTkEntry, CTkButton, and CTkTextbox. Arrange elements using pack, grid, or place geometry managers.", "status": "pending"}, {"id": 3, "title": "Implement Basic Button Actions", "description": "Write the initial callback functions for the Start and Stop buttons. These functions will contain the logic to initiate or terminate the proxy process.", "dependencies": [2], "details": "Define functions like `start_proxy_action()` and `stop_proxy_action()`. Initially, these might just print status or change button states.", "status": "pending"}, {"id": 4, "title": "Integrate Threading for Proxy Control", "description": "Modify the button actions to run the proxy server logic in a separate thread. This prevents the GUI from freezing while the proxy is running.", "dependencies": [3], "details": "Use Python's `threading` module. Create a thread for the proxy server process and manage its start/stop lifecycle from the GUI actions.", "status": "pending"}, {"id": 5, "title": "Implement Activity Log Functionality", "description": "Add functionality to update the activity log textbox with messages indicating proxy status, events, or errors. Ensure thread-safe updates if messages originate from the proxy thread.", "dependencies": [2, 4], "details": "Create a method to insert text into the log textbox. Implement a mechanism (e.g., queue, polling, or thread-safe method) to receive messages from the proxy thread and display them in the log.", "status": "pending"}]}, {"id": 29, "title": "Implement Integrated Chatbox UI", "description": "Implement the UI elements for the integrated chatbox within `gui_app.py`, including an input field, a display textbox, and a 'Send Chat' button.", "details": "In `gui_app.py`, add a `CTkEntry` for user input, a `CTkTextbox` for displaying the chat history, and a `CTkButton` labeled 'Send Chat'. Arrange these elements within the GUI layout.", "testStrategy": "Run `gui_app.py`. Verify the chatbox input field, display area, and send button are visible and correctly placed in the GUI window.", "priority": "medium", "dependencies": [28], "status": "done", "subtasks": [{"id": 1, "title": "Add Chat Display Area", "description": "Implement and integrate the UI component responsible for displaying the chat message history.", "dependencies": [], "details": "This component will typically be a scrollable area showing previous messages.", "status": "done"}, {"id": 2, "title": "Add Message Input Field", "description": "Implement and integrate the UI component where users can type their messages.", "dependencies": [], "details": "This will likely be a text input field, possibly multi-line.", "status": "done"}, {"id": 3, "title": "Add Send <PERSON>", "description": "Implement and integrate the UI component that triggers sending the message from the input field.", "dependencies": [], "details": "This button will be placed near the input field.", "status": "done"}]}, {"id": 30, "title": "Implement Integrated Chatbox Logic", "description": "Implement the logic in `gui_app.py` for the integrated chatbox to construct an LLM API request payload from user input, send it to the proxy's `/v1/gui_relay` endpoint using `httpx`, and display the LLM response in the chat display textbox.", "details": "In `gui_app.py`, implement the function triggered by the 'Send Chat' button or Enter key press in the input field. This function should get text from the input field, construct a JSON payload (e.g., `{'contents': [{'role': 'user', 'parts': [{'text': user_input}]}], 'model': CHAT_LLM_MODEL}` for Gemini format), send an async POST request to `http://localhost:8000/v1/gui_relay` using `httpx.AsyncClient` in a separate thread, wait for the response, extract the LLM's reply from the response body, and append both the user message and the LLM reply to the chat display textbox. Clear the input field after sending. Ensure robust error handling for network issues or LLM errors.", "testStrategy": "Run the proxy and GUI. Type a message in the chatbox and click 'Send Chat'. Verify the message appears in the chat history, the request is sent to the proxy (check proxy logs/console), the proxy forwards it to the LLM, and the LLM's response is received and displayed in the chat history.", "priority": "high", "dependencies": [26, 29], "status": "pending", "subtasks": []}, {"id": 31, "title": "Implement WebSocket Communication Setup", "description": "Implement the WebSocket endpoint (`/ws`) in `proxy_server.py` and the WebSocket client connection and basic message receiving loop in `gui_app.py`.", "details": "In `proxy_server.py`, add a WebSocket endpoint `async def websocket_endpoint(websocket: WebSocket):`. Accept the connection. Implement a loop to receive messages. In `gui_app.py`, implement an async function `_websocket_connect_and_listen()` that connects to `config.WEBSOCKET_URL` using `websockets.connect`. This function should run in a separate thread (with its own asyncio loop) managed by the GUI. Implement a loop within this function to receive messages from the WebSocket and put them into a `queue.Queue()` for the GUI's main thread to process (`_process_gui_queue`). Ensure connection retry logic in the GUI.", "testStrategy": "Run the proxy and GUI. Verify the WebSocket connection is established (check proxy/GUI logs). Send a test message from the proxy to the GUI via WS and verify the GUI receives it (e.g., prints to console or activity log). Disconnect/reconnect WS to test retry logic.", "priority": "high", "dependencies": [26, 28], "status": "pending", "subtasks": []}, {"id": 32, "title": "Implement Live Manipulation UI", "description": "Implement the 'Live Interception & Manipulation' UI elements in `gui_app.py`, including an editable textbox to display intercepted JSON and a 'Send Manipulated Message' button.", "details": "In `gui_app.py`, add a `CTkTextbox` specifically for displaying and editing intercepted request JSON. Configure it to be `state=\"disabled\"` by default and `state=\"normal\"` when an interception occurs. Add a `CTkButton` labeled 'Send Manipulated Message', also `state=\"disabled\"` by default. Arrange these elements in the GUI layout, perhaps in a dedicated section with clear labels.", "testStrategy": "Run `gui_app.py`. Verify the 'Live Interception & Manipulation' textbox and the 'Send Manipulated Message' button are visible, correctly placed, and initially disabled.", "priority": "medium", "dependencies": [28], "status": "pending", "subtasks": []}, {"id": 33, "title": "Enhance Proxy Interceptor for Pausing and Sending to GUI", "description": "Enhance the core HTTP proxy interceptor (`/v1/{full_path:path}`) in `proxy_server.py` to pause the request processing, assign a unique request ID, store the request context, and send the request body and ID to the GUI via WebSocket.", "details": "In the `proxy_llm_request` endpoint in `proxy_server.py`, before forwarding the request: Generate a unique `request_id` (e.g., using `uuid.uuid4()`). Store the request object or necessary context (headers, original URL, etc.) and an `asyncio.Future` in a dictionary keyed by `request_id` (e.g., `pending_requests`). Send a JSON message via WebSocket to all connected GUIs containing `type: 'intercepted_request'`, `request_id`, the request `headers`, and the request `body`. Await the result of the `asyncio.Future` with a timeout (e.g., 60 seconds). The request processing will pause here until the Future is set by the WebSocket handler receiving the manipulated message from the GUI. Handle `asyncio.TimeoutError` by returning an appropriate HTTP error.", "testStrategy": "Run the proxy and GUI. Send an LLM request from a client. Verify the request is paused (it should not immediately reach the LLM or return a response). Verify a message with `type: 'intercepted_request'`, `request_id`, and the request body/headers is sent from the proxy to the GUI via WebSocket (check GUI's activity log/interception display).", "priority": "high", "dependencies": [27, 31], "status": "pending", "subtasks": []}, {"id": 34, "title": "Implement GUI WebSocket Handling for Intercepted Requests", "description": "Implement WebSocket message handling in `gui_app.py` to receive `intercepted_request` messages from the proxy, display the request JSON in the manipulation textbox, enable editing, and store the associated `request_id`.", "details": "In `gui_app.py`, modify the `_process_gui_queue()` function to handle messages with `type: 'intercepted_request'`. When such a message is received, extract the `request_id`, `headers`, and `body`. Store the `request_id` in a class variable (`self.current_request_id`). Display the `body` (formatted JSON, e.g., using `json.dumps(..., indent=2)`) in the 'Live Interception & Manipulation' textbox. Configure this textbox to `state=\"normal\"` (editable). Enable the 'Send Manipulated Message' button. Update the GUI status or activity log to indicate a request is pending manipulation.", "testStrategy": "Run the proxy and GUI. Send an LLM request from a client. Verify the request JSON appears in the 'Live Interception & Manipulation' textbox, which becomes editable, and the 'Send Manipulated Message' button becomes enabled. The GUI's activity log should indicate a pending manipulation.", "priority": "high", "dependencies": [31, 32], "status": "pending", "subtasks": []}, {"id": 35, "title": "Implement GUI Logic to Send Manipulated Message", "description": "Implement the logic for the 'Send Manipulated Message' button in `gui_app.py` to get the edited JSON from the manipulation textbox, construct a `manipulated_message` WebSocket message including the stored `request_id`, and send it back to the proxy.", "details": "In `gui_app.py`, implement the function triggered by the 'Send Manipulated Message' button. Retrieve the text content from the 'Live Interception & Manipulation' textbox. Attempt to parse it as JSON. Retrieve the stored `self.current_request_id`. Construct a JSON message: `{'type': 'manipulated_message', 'request_id': self.current_request_id, 'manipulated_body': parsed_json_object}`. Send this JSON message to the proxy via the WebSocket connection using `asyncio.run_coroutine_threadsafe(self.websocket_client.send_text(...), self.websocket_loop)`. Clear the manipulation textbox, set its state back to `disabled`, disable the 'Send Manipulated Message' button, and clear `self.current_request_id`. Provide error feedback if JSON parsing fails.", "testStrategy": "Run the proxy and GUI. Send an LLM request. When the JSON appears in the GUI, edit it (both valid and invalid JSON to test error handling). Click 'Send Manipulated Message'. Verify a message with `type: 'manipulated_message'`, the correct `request_id`, and the edited body is sent from the GUI to the proxy via WebSocket (check proxy logs). Verify GUI elements (textbox, button) are disabled after sending.", "priority": "high", "dependencies": [32, 34], "status": "pending", "subtasks": []}, {"id": 36, "title": "Implement Proxy WebSocket Handling for Manipulated Messages", "description": "Implement WebSocket message handling in `proxy_server.py` to receive `manipulated_message` messages from the GUI, retrieve the corresponding paused request using the `request_id`, update its body with the manipulated content, and resume the request by setting the result of the associated `asyncio.Future`.", "details": "In the `websocket_endpoint` handler in `proxy_server.py`, add logic to process messages with `type: 'manipulated_message'`. Extract the `request_id` and `manipulated_body`. Look up the stored `asyncio.Future` in `pending_requests` using the `request_id`. Set the result of the `asyncio.Future` to `manipulated_body`, which will unblock the waiting `proxy_llm_request` endpoint. Handle cases where the `request_id` is not found (e.g., timeout occurred or invalid ID) by printing a warning. Remove the request from `pending_requests` after processing.", "testStrategy": "Run the proxy and GUI. Send an LLM request. When the JSON appears in the GUI, edit it and click 'Send Manipulated Message'. Verify the proxy receives the manipulated message, updates the request body internally, and the request successfully proceeds to the LLM provider with the *modified* body. Verify the LLM response is received by the original client and logged correctly in the DB.", "priority": "high", "dependencies": [33, 35], "status": "pending", "subtasks": []}], "metadata": {"created": "2025-06-20T12:46:47.487Z", "updated": "2025-06-21T11:17:58.203Z", "description": "Tasks for OAPIE-Lite development"}}}