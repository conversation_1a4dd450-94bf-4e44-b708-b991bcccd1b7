# Task ID: 34
# Title: Implement GUI WebSocket Handling for Intercepted Requests
# Status: pending
# Dependencies: 31, 32
# Priority: high
# Description: Implement WebSocket message handling in `gui_app.py` to receive `intercepted_request` messages from the proxy, display the request JSO<PERSON> in the manipulation textbox, and store the associated `request_id`.
# Details:
In `gui_app.py`, modify the `_process_gui_queue()` function to handle messages with `type: 'intercepted_request'`. When such a message is received, extract the `request_id` and `body`. Store the `request_id` (e.g., in a class variable). Display the `body` (formatted JSON) in the 'Live Interception & Manipulation' textbox. Update the GUI status or activity log to indicate a request is pending manipulation.

# Test Strategy:
Run the proxy and GUI. Send an LLM request from a client. Verify the request <PERSON><PERSON><PERSON> appears in the 'Live Interception & Manipulation' textbox in the GUI, and the GUI indicates it's waiting for input.
