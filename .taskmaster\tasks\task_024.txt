# Task ID: 24
# Title: Implement Database Logger Module (db_logger.py)
# Status: pending
# Dependencies: 23
# Priority: high
# Description: Implement the `db_logger.py` module including functions for database connection (`get_db_connection`) and logging messages (`log_message_to_db`), using `psycopg2-binary`.
# Details:
Create `db_logger.py`. Implement `get_db_connection()` using `psycopg2.connect` with credentials from `config.py`. Implement `log_message_to_db(data)` function that takes a dictionary of log data, connects to the DB, and inserts a row into the `messages_log` table using a parameterized SQL query to prevent injection.

# Test Strategy:
Write a simple script to call `get_db_connection()` and `log_message_to_db()` with test data. Verify a new row appears in the `messages_log` table in the database.
